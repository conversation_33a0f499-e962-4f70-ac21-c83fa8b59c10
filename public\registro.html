<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrarse - CanchaCL</title>
    <meta name="description" content="Crea tu cuenta en CanchaCL y accede a todas las funciones para reservar canchas deportivas.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="login.html" class="btn btn-outline btn-sm">Iniciar Sesión</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="login.html" class="btn btn-outline btn-sm">Entrar</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="login.html">Iniciar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <section class="section">
            <div class="container">
                <div style="max-width: 500px; margin: 0 auto;">
                    <div class="card">
                        <div class="card-header text-center">
                            <h1>Crear Cuenta</h1>
                            <p class="text-gris-600">Únete a CanchaCL y comienza a reservar</p>
                        </div>
                        
                        <div class="card-body">
                            <!-- Alertas -->
                            <div id="registro-alerts"></div>
                            
                            <form id="registro-form">
                                <!-- Tipo de cuenta -->
                                <div class="form-group">
                                    <label class="form-label">Tipo de cuenta</label>
                                    <div class="grid grid-cols-1 gap-3">
                                        <div class="form-check">
                                            <input type="radio" id="tipo-usuario" name="tipo" value="usuario" class="form-check-input" checked>
                                            <label for="tipo-usuario" class="form-check-label">
                                                <strong>Usuario</strong><br>
                                                <span class="text-sm text-gris-600">Para reservar canchas deportivas</span>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check">
                                            <input type="radio" id="tipo-dueno" name="tipo" value="dueno" class="form-check-input">
                                            <label for="tipo-dueno" class="form-check-label">
                                                <strong>Dueño de cancha</strong><br>
                                                <span class="text-sm text-gris-600">Para publicar y gestionar canchas</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Datos personales -->
                                <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                                    <div class="form-group">
                                        <label for="nombre" class="form-label">Nombre *</label>
                                        <input 
                                            type="text" 
                                            id="nombre" 
                                            name="nombre" 
                                            class="form-input" 
                                            placeholder="Tu nombre"
                                            required
                                            autocomplete="given-name"
                                        >
                                        <div class="form-error" id="nombre-error"></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="apellido" class="form-label">Apellido *</label>
                                        <input 
                                            type="text" 
                                            id="apellido" 
                                            name="apellido" 
                                            class="form-input" 
                                            placeholder="Tu apellido"
                                            required
                                            autocomplete="family-name"
                                        >
                                        <div class="form-error" id="apellido-error"></div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email" class="form-label">Email *</label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        name="email" 
                                        class="form-input" 
                                        placeholder="<EMAIL>"
                                        required
                                        autocomplete="email"
                                    >
                                    <div class="form-error" id="email-error"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="telefono" class="form-label">Teléfono *</label>
                                    <input 
                                        type="tel" 
                                        id="telefono" 
                                        name="telefono" 
                                        class="form-input" 
                                        placeholder="+56 9 XXXX XXXX"
                                        required
                                        autocomplete="tel"
                                    >
                                    <div class="form-help">Formato: +56 9 XXXX XXXX</div>
                                    <div class="form-error" id="telefono-error"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="password" class="form-label">Contraseña *</label>
                                    <input 
                                        type="password" 
                                        id="password" 
                                        name="password" 
                                        class="form-input" 
                                        placeholder="Mínimo 6 caracteres"
                                        required
                                        autocomplete="new-password"
                                    >
                                    <div class="form-help">Mínimo 6 caracteres</div>
                                    <div class="form-error" id="password-error"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirm-password" class="form-label">Confirmar contraseña *</label>
                                    <input 
                                        type="password" 
                                        id="confirm-password" 
                                        name="confirmPassword" 
                                        class="form-input" 
                                        placeholder="Repite tu contraseña"
                                        required
                                        autocomplete="new-password"
                                    >
                                    <div class="form-error" id="confirm-password-error"></div>
                                </div>
                                
                                <!-- Términos y condiciones -->
                                <div class="form-check">
                                    <input type="checkbox" id="acepta-terminos" name="aceptaTerminos" class="form-check-input" required>
                                    <label for="acepta-terminos" class="form-check-label">
                                        Acepto los <a href="#" class="text-rojo">Términos y Condiciones</a> 
                                        y la <a href="#" class="text-rojo">Política de Privacidad</a> *
                                    </label>
                                    <div class="form-error" id="acepta-terminos-error"></div>
                                </div>
                                
                                <!-- Newsletter -->
                                <div class="form-check">
                                    <input type="checkbox" id="newsletter" name="newsletter" class="form-check-input">
                                    <label for="newsletter" class="form-check-label">
                                        Quiero recibir ofertas especiales y novedades por email
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-full btn-lg" id="registro-btn">
                                    Crear mi cuenta
                                </button>
                            </form>
                        </div>
                        
                        <div class="card-footer text-center">
                            <p class="text-sm text-gris-600">
                                ¿Ya tienes cuenta? 
                                <a href="login.html" class="text-rojo font-medium">Inicia sesión aquí</a>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Información adicional para dueños -->
                    <div class="card" id="info-dueno" style="margin-top: var(--espacio-xl); display: none;">
                        <div class="card-header">
                            <h3>Información para dueños de cancha</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 gap-4">
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M2 17L12 22L22 17" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M2 12L12 17L22 12" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Gestión completa</h4>
                                        <p class="text-sm text-gris-600">Panel de administración para gestionar canchas, horarios y reservas</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <circle cx="8.5" cy="7" r="4" stroke="var(--rojo)" stroke-width="2"/>
                                            <path d="M20 8V14" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M23 11H17" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Más clientes</h4>
                                        <p class="text-sm text-gris-600">Llega a miles de usuarios que buscan canchas en tu zona</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <line x1="12" y1="1" x2="12" y2="23" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6312 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6312 13.6815 18 14.5717 18 15.5C18 16.4283 17.6312 17.3185 16.9749 17.9749C16.3185 18.6312 15.4283 19 14.5 19H6" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Sin comisiones</h4>
                                        <p class="text-sm text-gris-600">Periodo de prueba gratuito sin comisiones por reservas</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar si ya está logueado
            const token = localStorage.getItem('token');
            if (token) {
                window.location.href = 'perfil.html';
                return;
            }
            
            // Configurar formulario de registro
            const registroForm = document.getElementById('registro-form');
            const registroBtn = document.getElementById('registro-btn');
            const alertsContainer = document.getElementById('registro-alerts');
            const infoDueno = document.getElementById('info-dueno');
            
            // Mostrar/ocultar información para dueños
            document.querySelectorAll('input[name="tipo"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    infoDueno.style.display = this.value === 'dueno' ? 'block' : 'none';
                });
            });
            
            // Formatear teléfono mientras se escribe
            document.getElementById('telefono').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.startsWith('56')) {
                    value = value.substring(2);
                }
                if (value.length > 0) {
                    if (value.length <= 1) {
                        value = '+56 ' + value;
                    } else if (value.length <= 5) {
                        value = '+56 ' + value.substring(0, 1) + ' ' + value.substring(1);
                    } else {
                        value = '+56 ' + value.substring(0, 1) + ' ' + value.substring(1, 5) + ' ' + value.substring(5, 9);
                    }
                }
                e.target.value = value;
            });
            
            registroForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Limpiar errores anteriores
                clearErrors();
                clearAlerts();
                
                // Obtener datos del formulario
                const formData = new FormData(registroForm);
                const datos = {
                    tipo: formData.get('tipo'),
                    nombre: formData.get('nombre').trim(),
                    apellido: formData.get('apellido').trim(),
                    email: formData.get('email').trim(),
                    telefono: formData.get('telefono').trim(),
                    password: formData.get('password'),
                    confirmPassword: formData.get('confirmPassword'),
                    aceptaTerminos: formData.get('aceptaTerminos') === 'on',
                    newsletter: formData.get('newsletter') === 'on'
                };
                
                // Validaciones
                let hasErrors = false;
                
                if (!datos.nombre || datos.nombre.length < 2) {
                    showFieldError('nombre', 'El nombre debe tener al menos 2 caracteres');
                    hasErrors = true;
                }
                
                if (!datos.apellido || datos.apellido.length < 2) {
                    showFieldError('apellido', 'El apellido debe tener al menos 2 caracteres');
                    hasErrors = true;
                }
                
                if (!datos.email) {
                    showFieldError('email', 'El email es requerido');
                    hasErrors = true;
                } else if (!isValidEmail(datos.email)) {
                    showFieldError('email', 'El email no tiene un formato válido');
                    hasErrors = true;
                }
                
                if (!datos.telefono) {
                    showFieldError('telefono', 'El teléfono es requerido');
                    hasErrors = true;
                } else if (!isValidChileanPhone(datos.telefono)) {
                    showFieldError('telefono', 'El teléfono debe tener formato chileno (+56 9 XXXX XXXX)');
                    hasErrors = true;
                }
                
                if (!datos.password) {
                    showFieldError('password', 'La contraseña es requerida');
                    hasErrors = true;
                } else if (datos.password.length < 6) {
                    showFieldError('password', 'La contraseña debe tener al menos 6 caracteres');
                    hasErrors = true;
                }
                
                if (datos.password !== datos.confirmPassword) {
                    showFieldError('confirm-password', 'Las contraseñas no coinciden');
                    hasErrors = true;
                }
                
                if (!datos.aceptaTerminos) {
                    showFieldError('acepta-terminos', 'Debes aceptar los términos y condiciones');
                    hasErrors = true;
                }
                
                if (hasErrors) return;
                
                // Mostrar estado de carga
                const originalText = registroBtn.textContent;
                registroBtn.disabled = true;
                registroBtn.innerHTML = '<div class="loading" style="margin-right: var(--espacio-sm);"></div>Creando cuenta...';
                
                try {
                    // Llamar a la API de registro
                    if (window.API) {
                        const response = await window.API.registro(datos);
                        
                        if (response.success) {
                            // Guardar datos de sesión
                            localStorage.setItem('token', response.data.token);
                            localStorage.setItem('usuario', JSON.stringify(response.data.usuario));
                            
                            // Mostrar mensaje de éxito
                            showAlert('success', '¡Cuenta creada exitosamente! Redirigiendo...');
                            
                            // Redirigir después de un breve delay
                            setTimeout(() => {
                                window.location.href = 'perfil.html';
                            }, 2000);
                            
                        } else {
                            if (response.errores && Array.isArray(response.errores)) {
                                response.errores.forEach(error => {
                                    showAlert('error', error);
                                });
                            } else {
                                showAlert('error', response.message || 'Error al crear la cuenta');
                            }
                        }
                    } else {
                        showAlert('error', 'Error de conexión. Inténtalo nuevamente.');
                    }
                    
                } catch (error) {
                    console.error('Error en registro:', error);
                    showAlert('error', 'Error de conexión. Inténtalo nuevamente.');
                } finally {
                    // Restaurar botón
                    registroBtn.disabled = false;
                    registroBtn.textContent = originalText;
                }
            });
            
            // Funciones auxiliares
            function clearErrors() {
                document.querySelectorAll('.form-error').forEach(error => {
                    error.textContent = '';
                });
                document.querySelectorAll('.form-input').forEach(input => {
                    input.classList.remove('error');
                });
            }
            
            function showFieldError(fieldName, message) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const inputElement = document.getElementById(fieldName);
                
                if (errorElement) errorElement.textContent = message;
                if (inputElement) inputElement.classList.add('error');
            }
            
            function clearAlerts() {
                alertsContainer.innerHTML = '';
            }
            
            function showAlert(type, message) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert ${alertClass}`;
                alertDiv.textContent = message;
                alertsContainer.appendChild(alertDiv);
            }
            
            function isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }
            
            function isValidChileanPhone(phone) {
                return /^\+56\s?[0-9]\s?[0-9]{4}\s?[0-9]{4}$/.test(phone);
            }
        });
    </script>
</body>
</html>
