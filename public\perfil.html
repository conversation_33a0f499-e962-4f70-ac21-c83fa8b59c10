<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Perfil - CanchaCL</title>
    <meta name="description" content="Gestiona tu perfil, reservas y configuración en CanchaCL.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="perfil.html">Mi Perfil</a></li>
                            <li><a href="#" id="logout-desktop">Cerrar Sesión</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <span class="text-sm" id="user-name-mobile">Usuario</span>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="perfil.html">Mi Perfil</a></li>
                <li><a href="admin/dashboard.html" id="admin-link" style="display: none;">Panel Admin</a></li>
                <li><a href="#" id="logout-mobile">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Profile Header -->
        <section class="section" style="background-color: var(--gris-100);">
            <div class="container">
                <div class="flex items-center gap-4">
                    <div style="width: 80px; height: 80px; background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--blanco); font-size: var(--font-size-2xl); font-weight: 700;">
                        <span id="user-initials">U</span>
                    </div>
                    <div>
                        <h1 id="user-full-name">Usuario</h1>
                        <p class="text-gris-600" id="user-email"><EMAIL></p>
                        <div class="chip chip-deporte" id="user-type">Usuario</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Content -->
        <section class="section">
            <div class="container">
                <div class="grid grid-cols-1 grid-cols-lg-4 gap-8">
                    <!-- Sidebar -->
                    <div class="col-span-1">
                        <div class="card">
                            <div class="card-body">
                                <nav>
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <li style="margin-bottom: var(--espacio-sm);">
                                            <a href="#" class="profile-nav-link active" data-section="reservas">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                    <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                                                    <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                                                    <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                                Mis Reservas
                                            </a>
                                        </li>
                                        <li style="margin-bottom: var(--espacio-sm);">
                                            <a href="#" class="profile-nav-link" data-section="favoritos">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                Favoritos
                                            </a>
                                        </li>
                                        <li style="margin-bottom: var(--espacio-sm);">
                                            <a href="#" class="profile-nav-link" data-section="datos">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2"/>
                                                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                                Mis Datos
                                            </a>
                                        </li>
                                        <li style="margin-bottom: var(--espacio-sm);">
                                            <a href="#" class="profile-nav-link" data-section="configuracion">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                                    <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                Configuración
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div class="col-span-3">
                        <!-- Mis Reservas -->
                        <div id="section-reservas" class="profile-section">
                            <div class="card">
                                <div class="card-header">
                                    <h2>Mis Reservas</h2>
                                    <p class="text-gris-600">Gestiona tus reservas actuales y pasadas</p>
                                </div>
                                <div class="card-body">
                                    <!-- Loading state -->
                                    <div id="reservas-loading" class="text-center" style="padding: var(--espacio-xl) 0;">
                                        <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                                        <p>Cargando reservas...</p>
                                    </div>
                                    
                                    <!-- Reservas list -->
                                    <div id="reservas-list" style="display: none;">
                                        <!-- Se llenan dinámicamente -->
                                    </div>
                                    
                                    <!-- No reservas -->
                                    <div id="no-reservas" class="text-center" style="padding: var(--espacio-xl) 0; display: none;">
                                        <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-lg); opacity: 0.5;">
                                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="var(--gris-400)" stroke-width="2"/>
                                                <line x1="16" y1="2" x2="16" y2="6" stroke="var(--gris-400)" stroke-width="2"/>
                                                <line x1="8" y1="2" x2="8" y2="6" stroke="var(--gris-400)" stroke-width="2"/>
                                                <line x1="3" y1="10" x2="21" y2="10" stroke="var(--gris-400)" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <h3>No tienes reservas</h3>
                                        <p>Cuando hagas tu primera reserva aparecerá aquí.</p>
                                        <a href="resultados.html" class="btn btn-primary">Buscar canchas</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Favoritos -->
                        <div id="section-favoritos" class="profile-section" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h2>Canchas Favoritas</h2>
                                    <p class="text-gris-600">Tus canchas guardadas para acceso rápido</p>
                                </div>
                                <div class="card-body">
                                    <div class="text-center" style="padding: var(--espacio-xl) 0;">
                                        <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-lg); opacity: 0.5;">
                                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="var(--gris-400)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                        <h3>No tienes favoritos</h3>
                                        <p>Guarda canchas como favoritas para encontrarlas fácilmente.</p>
                                        <a href="resultados.html" class="btn btn-primary">Explorar canchas</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mis Datos -->
                        <div id="section-datos" class="profile-section" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h2>Mis Datos Personales</h2>
                                    <p class="text-gris-600">Actualiza tu información personal</p>
                                </div>
                                <div class="card-body">
                                    <form id="datos-form">
                                        <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                                            <div class="form-group">
                                                <label for="edit-nombre" class="form-label">Nombre</label>
                                                <input type="text" id="edit-nombre" name="nombre" class="form-input" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="edit-apellido" class="form-label">Apellido</label>
                                                <input type="text" id="edit-apellido" name="apellido" class="form-input" required>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="edit-email" class="form-label">Email</label>
                                            <input type="email" id="edit-email" name="email" class="form-input" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="edit-telefono" class="form-label">Teléfono</label>
                                            <input type="tel" id="edit-telefono" name="telefono" class="form-input" required>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary">Guardar cambios</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Configuración -->
                        <div id="section-configuracion" class="profile-section" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h2>Configuración</h2>
                                    <p class="text-gris-600">Personaliza tu experiencia en CanchaCL</p>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <h4>Notificaciones</h4>
                                        <div class="form-check">
                                            <input type="checkbox" id="notif-email" class="form-check-input" checked>
                                            <label for="notif-email" class="form-check-label">
                                                Recibir notificaciones por email
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" id="notif-ofertas" class="form-check-input">
                                            <label for="notif-ofertas" class="form-check-label">
                                                Recibir ofertas especiales
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <h4>Privacidad</h4>
                                        <div class="form-check">
                                            <input type="checkbox" id="perfil-publico" class="form-check-input">
                                            <label for="perfil-publico" class="form-check-label">
                                                Perfil público (otros usuarios pueden ver tu actividad)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <h4>Cuenta</h4>
                                        <button type="button" class="btn btn-outline" id="cambiar-password">
                                            Cambiar contraseña
                                        </button>
                                        <button type="button" class="btn btn-outline text-rojo" id="eliminar-cuenta" style="margin-left: var(--espacio-md);">
                                            Eliminar cuenta
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        let usuario = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Verificar autenticación
            const token = localStorage.getItem('token');
            const usuarioData = localStorage.getItem('usuario');
            
            if (!token || !usuarioData) {
                window.location.href = 'login.html?redirect=perfil.html';
                return;
            }
            
            try {
                usuario = JSON.parse(usuarioData);
                inicializarPerfil();
                cargarReservas();
            } catch (error) {
                console.error('Error parsing user data:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('usuario');
                window.location.href = 'login.html';
            }
        });

        function inicializarPerfil() {
            // Mostrar información del usuario
            const initials = (usuario.nombre.charAt(0) + (usuario.apellido?.charAt(0) || '')).toUpperCase();
            document.getElementById('user-initials').textContent = initials;
            document.getElementById('user-full-name').textContent = `${usuario.nombre} ${usuario.apellido || ''}`;
            document.getElementById('user-email').textContent = usuario.email;
            document.getElementById('user-name-mobile').textContent = usuario.nombre;
            
            const userType = document.getElementById('user-type');
            userType.textContent = usuario.tipo === 'admin' ? 'Administrador' : 
                                  usuario.tipo === 'dueno' ? 'Dueño' : 'Usuario';
            
            // Mostrar enlace admin si es admin o dueño
            if (usuario.tipo === 'admin' || usuario.tipo === 'dueno') {
                document.getElementById('admin-link').style.display = 'block';
            }
            
            // Llenar formulario de datos
            document.getElementById('edit-nombre').value = usuario.nombre;
            document.getElementById('edit-apellido').value = usuario.apellido || '';
            document.getElementById('edit-email').value = usuario.email;
            document.getElementById('edit-telefono').value = usuario.telefono || '';
            
            // Configurar navegación
            configurarNavegacion();
            
            // Configurar logout
            document.getElementById('logout-desktop').addEventListener('click', logout);
            document.getElementById('logout-mobile').addEventListener('click', logout);
        }

        function configurarNavegacion() {
            const navLinks = document.querySelectorAll('.profile-nav-link');
            const sections = document.querySelectorAll('.profile-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetSection = this.dataset.section;
                    
                    // Actualizar navegación activa
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Mostrar sección correspondiente
                    sections.forEach(s => s.style.display = 'none');
                    document.getElementById(`section-${targetSection}`).style.display = 'block';
                    
                    // Cargar datos específicos de la sección
                    if (targetSection === 'reservas') {
                        cargarReservas();
                    }
                });
            });
        }

        function cargarReservas() {
            const loadingElement = document.getElementById('reservas-loading');
            const listElement = document.getElementById('reservas-list');
            const noReservasElement = document.getElementById('no-reservas');
            
            loadingElement.style.display = 'block';
            listElement.style.display = 'none';
            noReservasElement.style.display = 'none';
            
            if (window.API) {
                window.API.getReservas({ usuario_id: usuario.id }).then(response => {
                    loadingElement.style.display = 'none';
                    
                    if (response.success && response.data.length > 0) {
                        listElement.style.display = 'block';
                        renderReservas(response.data);
                    } else {
                        noReservasElement.style.display = 'block';
                    }
                }).catch(error => {
                    console.error('Error cargando reservas:', error);
                    loadingElement.style.display = 'none';
                    noReservasElement.style.display = 'block';
                });
            }
        }

        function renderReservas(reservas) {
            const container = document.getElementById('reservas-list');
            container.innerHTML = '';
            
            reservas.forEach(reserva => {
                const reservaCard = document.createElement('div');
                reservaCard.className = 'card';
                reservaCard.style.marginBottom = 'var(--espacio-md)';
                
                const estadoClass = reserva.estado === 'confirmada' ? 'chip-disponible' : 
                                   reserva.estado === 'pendiente' ? 'chip-pocas' : 'chip-agotado';
                
                reservaCard.innerHTML = `
                    <div class="card-body">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4>${reserva.cancha?.nombre || 'Cancha'}</h4>
                                <p class="text-sm text-gris-600">${reserva.cancha?.direccion || ''}</p>
                                <p class="text-sm">
                                    <strong>Fecha:</strong> ${formatearFecha(reserva.fecha)} - ${reserva.hora}<br>
                                    <strong>Duración:</strong> ${reserva.duracion} hora${reserva.duracion > 1 ? 's' : ''}<br>
                                    <strong>Total:</strong> ${reserva.totalFormateado}
                                </p>
                            </div>
                            <div class="text-right">
                                <div class="chip ${estadoClass}">${reserva.estado}</div>
                                <p class="text-xs text-gris-500" style="margin-top: var(--espacio-xs);">
                                    Reserva #${reserva.id}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
                
                container.appendChild(reservaCard);
            });
        }

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('usuario');
            window.location.href = 'index.html';
        }

        function formatearFecha(fecha) {
            const date = new Date(fecha + 'T00:00:00');
            return date.toLocaleDateString('es-CL', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    </script>
    
    <style>
        .profile-nav-link {
            display: flex;
            align-items: center;
            gap: var(--espacio-sm);
            padding: var(--espacio-sm) var(--espacio-md);
            color: var(--gris-600);
            text-decoration: none;
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
        }
        
        .profile-nav-link:hover {
            background-color: var(--gris-100);
            color: var(--negro);
            text-decoration: none;
        }
        
        .profile-nav-link.active {
            background-color: var(--rojo-100);
            color: var(--rojo);
        }
    </style>
</body>
</html>
