<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Canchas - CanchaCL Admin</title>
    <meta name="description" content="Gestiona tus canchas deportivas: agregar, editar, eliminar y configurar horarios y precios.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../../assets/css/base.css">
    <link rel="stylesheet" href="../../assets/css/layout.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    Cancha<span class="accent">CL</span> <span class="text-sm text-gris-600">Admin</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="dashboard.html">Dashboard</a></li>
                            <li><a href="canchas.html">Canchas</a></li>
                            <li><a href="reservas.html">Reservas</a></li>
                            <li><a href="../perfil.html">Mi Perfil</a></li>
                            <li><a href="#" id="logout-desktop">Cerrar Sesión</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <span class="text-sm" id="admin-name-mobile">Admin</span>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="canchas.html">Canchas</a></li>
                <li><a href="reservas.html">Reservas</a></li>
                <li><a href="../perfil.html">Mi Perfil</a></li>
                <li><a href="../index.html">Ver sitio público</a></li>
                <li><a href="#" id="logout-mobile">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Page Header -->
        <section class="section" style="background-color: var(--gris-100); padding-top: var(--espacio-lg); padding-bottom: var(--espacio-lg);">
            <div class="container">
                <div class="admin-header">
                    <div>
                        <h1>Gestión de Canchas</h1>
                        <p class="text-gris-600">Administra tus canchas deportivas</p>
                    </div>
                    <button class="btn btn-primary" id="btn-nueva-cancha">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Nueva Cancha
                    </button>
                </div>
            </div>
        </section>

        <!-- Canchas List -->
        <section class="section">
            <div class="container">
                <!-- Filters -->
                <div class="card" style="margin-bottom: var(--espacio-xl);">
                    <div class="card-body">
                        <div class="grid grid-cols-1 grid-cols-md-4 gap-4">
                            <div class="form-group mb-0">
                                <label for="filter-deporte" class="form-label">Deporte</label>
                                <select id="filter-deporte" class="form-input form-select">
                                    <option value="">Todos los deportes</option>
                                    <option value="futbol">Fútbol</option>
                                    <option value="tenis">Tenis</option>
                                    <option value="padel">Pádel</option>
                                    <option value="basquet">Básquetbol</option>
                                    <option value="baby_futbol">Baby Fútbol</option>
                                </select>
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="filter-estado" class="form-label">Estado</label>
                                <select id="filter-estado" class="form-input form-select">
                                    <option value="">Todos los estados</option>
                                    <option value="activa">Activa</option>
                                    <option value="inactiva">Inactiva</option>
                                    <option value="mantenimiento">En mantenimiento</option>
                                </select>
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="filter-buscar" class="form-label">Buscar</label>
                                <input type="text" id="filter-buscar" class="form-input" placeholder="Nombre o ubicación">
                            </div>
                            
                            <div class="flex items-end">
                                <button class="btn btn-outline" id="btn-limpiar-filtros">Limpiar filtros</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Canchas Table -->
                <div class="card">
                    <div class="card-header">
                        <h3>Mis Canchas</h3>
                        <span class="text-sm text-gris-600" id="canchas-count">0 canchas</span>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <!-- Loading state -->
                        <div id="canchas-loading" class="text-center" style="padding: var(--espacio-xl) 0;">
                            <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                            <p>Cargando canchas...</p>
                        </div>
                        
                        <!-- Table -->
                        <div id="canchas-table-container" style="display: none; overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Cancha</th>
                                        <th>Deporte</th>
                                        <th>Comuna</th>
                                        <th>Precio/Hora</th>
                                        <th>Estado</th>
                                        <th>Rating</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="canchas-table-body">
                                    <!-- Se llena dinámicamente -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- No canchas -->
                        <div id="no-canchas" class="text-center" style="padding: var(--espacio-xl) 0; display: none;">
                            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-lg); opacity: 0.5;">
                                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="11" cy="11" r="8" stroke="var(--gris-400)" stroke-width="2"/>
                                    <path d="M21 21L16.65 16.65" stroke="var(--gris-400)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <h3>No tienes canchas registradas</h3>
                            <p>Agrega tu primera cancha para comenzar a recibir reservas.</p>
                            <button class="btn btn-primary" id="btn-primera-cancha">Agregar mi primera cancha</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal Nueva/Editar Cancha -->
    <div class="modal-backdrop" id="modal-cancha-backdrop">
        <div class="modal" id="modal-cancha">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-cancha-title">Nueva Cancha</h3>
                <button class="modal-close" id="modal-cancha-close">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form id="cancha-form">
                    <input type="hidden" id="cancha-id" name="id">
                    
                    <div class="form-group">
                        <label for="cancha-nombre" class="form-label">Nombre de la cancha *</label>
                        <input type="text" id="cancha-nombre" name="nombre" class="form-input" required>
                    </div>
                    
                    <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                        <div class="form-group">
                            <label for="cancha-deporte" class="form-label">Deporte *</label>
                            <select id="cancha-deporte" name="deporte" class="form-input form-select" required>
                                <option value="">Seleccionar deporte</option>
                                <option value="futbol">Fútbol</option>
                                <option value="tenis">Tenis</option>
                                <option value="padel">Pádel</option>
                                <option value="basquet">Básquetbol</option>
                                <option value="baby_futbol">Baby Fútbol</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="cancha-superficie" class="form-label">Superficie *</label>
                            <select id="cancha-superficie" name="superficie" class="form-input form-select" required>
                                <option value="">Seleccionar superficie</option>
                                <option value="Pasto natural">Pasto natural</option>
                                <option value="Pasto sintético">Pasto sintético</option>
                                <option value="Arcilla">Arcilla</option>
                                <option value="Cemento">Cemento</option>
                                <option value="Parquet">Parquet</option>
                                <option value="Césped sintético">Césped sintético</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="cancha-direccion" class="form-label">Dirección *</label>
                        <input type="text" id="cancha-direccion" name="direccion" class="form-input" required>
                    </div>
                    
                    <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                        <div class="form-group">
                            <label for="cancha-comuna" class="form-label">Comuna *</label>
                            <select id="cancha-comuna" name="comuna" class="form-input form-select" required>
                                <option value="">Seleccionar comuna</option>
                                <!-- Se llenan dinámicamente -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="cancha-precio" class="form-label">Precio por hora (CLP) *</label>
                            <input type="number" id="cancha-precio" name="precioHoraCLP" class="form-input" min="1000" step="1000" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="cancha-descripcion" class="form-label">Descripción</label>
                        <textarea id="cancha-descripcion" name="descripcion" class="form-input form-textarea" placeholder="Describe tu cancha, servicios incluidos, etc."></textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                        <div class="form-group">
                            <label for="cancha-telefono" class="form-label">Teléfono de contacto</label>
                            <input type="tel" id="cancha-telefono" name="telefono" class="form-input" placeholder="+56 2 2234 5678">
                        </div>
                        
                        <div class="form-group">
                            <label for="cancha-email" class="form-label">Email de contacto</label>
                            <input type="email" id="cancha-email" name="email" class="form-input" placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Servicios incluidos</label>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="form-check">
                                <input type="checkbox" id="servicio-estacionamiento" name="servicios" value="Estacionamiento" class="form-check-input">
                                <label for="servicio-estacionamiento" class="form-check-label">Estacionamiento</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="servicio-camarines" name="servicios" value="Camarines" class="form-check-input">
                                <label for="servicio-camarines" class="form-check-label">Camarines</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="servicio-iluminacion" name="servicios" value="Iluminación" class="form-check-input">
                                <label for="servicio-iluminacion" class="form-check-label">Iluminación</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="servicio-seguridad" name="servicios" value="Seguridad" class="form-check-input">
                                <label for="servicio-seguridad" class="form-check-label">Seguridad</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="servicio-cafeteria" name="servicios" value="Cafetería" class="form-check-input">
                                <label for="servicio-cafeteria" class="form-check-label">Cafetería</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="servicio-proshop" name="servicios" value="Pro Shop" class="form-check-input">
                                <label for="servicio-proshop" class="form-check-label">Pro Shop</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-cancha-cancelar">Cancelar</button>
                <button type="submit" form="cancha-form" class="btn btn-primary" id="modal-cancha-guardar">Guardar cancha</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL Admin</h3>
                    <p>Panel de administración para dueños de canchas deportivas.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Soporte</h3>
                    <ul>
                        <li><a href="#">Centro de Ayuda</a></li>
                        <li><a href="#">Contactar Soporte</a></li>
                        <li><a href="#">Guías y Tutoriales</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos de Servicio</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Comisiones y Tarifas</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Panel Administrativo.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../assets/js/app.js" defer></script>
    <script src="../../assets/js/api.js" defer></script>
    <script src="../../assets/js/ui.js" defer></script>
    <script>
        let usuario = null;
        let canchas = [];
        let canchasFiltradas = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Verificar autenticación y permisos
            const token = localStorage.getItem('token');
            const usuarioData = localStorage.getItem('usuario');
            
            if (!token || !usuarioData) {
                window.location.href = '../login.html?redirect=admin/canchas.html';
                return;
            }
            
            try {
                usuario = JSON.parse(usuarioData);
                
                if (usuario.tipo !== 'admin' && usuario.tipo !== 'dueno') {
                    alert('No tienes permisos para acceder al panel administrativo.');
                    window.location.href = '../perfil.html';
                    return;
                }
                
                inicializarPagina();
                cargarComunas();
                cargarCanchas();
                
            } catch (error) {
                console.error('Error parsing user data:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('usuario');
                window.location.href = '../login.html';
            }
        });

        function inicializarPagina() {
            // Mostrar nombre del admin
            document.getElementById('admin-name-mobile').textContent = usuario.nombre;
            
            // Configurar event listeners
            document.getElementById('btn-nueva-cancha').addEventListener('click', () => abrirModalCancha());
            document.getElementById('btn-primera-cancha').addEventListener('click', () => abrirModalCancha());
            document.getElementById('modal-cancha-close').addEventListener('click', cerrarModalCancha);
            document.getElementById('modal-cancha-cancelar').addEventListener('click', cerrarModalCancha);
            document.getElementById('modal-cancha-backdrop').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) cerrarModalCancha();
            });
            
            // Configurar filtros
            document.getElementById('filter-deporte').addEventListener('change', aplicarFiltros);
            document.getElementById('filter-estado').addEventListener('change', aplicarFiltros);
            document.getElementById('filter-buscar').addEventListener('input', aplicarFiltros);
            document.getElementById('btn-limpiar-filtros').addEventListener('click', limpiarFiltros);
            
            // Configurar formulario
            document.getElementById('cancha-form').addEventListener('submit', guardarCancha);
            
            // Configurar logout
            document.getElementById('logout-desktop').addEventListener('click', logout);
            document.getElementById('logout-mobile').addEventListener('click', logout);
        }

        function cargarComunas() {
            if (window.API) {
                window.API.getComunas().then(response => {
                    if (response.success) {
                        const comunaSelect = document.getElementById('cancha-comuna');
                        response.data.forEach(comuna => {
                            const option = document.createElement('option');
                            option.value = comuna.nombre;
                            option.textContent = comuna.nombre;
                            comunaSelect.appendChild(option);
                        });
                    }
                }).catch(console.error);
            }
        }

        function cargarCanchas() {
            const loadingElement = document.getElementById('canchas-loading');
            const tableContainer = document.getElementById('canchas-table-container');
            const noCanchasElement = document.getElementById('no-canchas');
            
            loadingElement.style.display = 'block';
            tableContainer.style.display = 'none';
            noCanchasElement.style.display = 'none';
            
            if (window.API) {
                window.API.getCanchas().then(response => {
                    loadingElement.style.display = 'none';
                    
                    if (response.success && response.data.length > 0) {
                        canchas = response.data;
                        canchasFiltradas = [...canchas];
                        tableContainer.style.display = 'block';
                        renderCanchasTable();
                        actualizarContador();
                    } else {
                        noCanchasElement.style.display = 'block';
                    }
                }).catch(error => {
                    console.error('Error cargando canchas:', error);
                    loadingElement.style.display = 'none';
                    noCanchasElement.style.display = 'block';
                });
            }
        }

        function renderCanchasTable() {
            const tbody = document.getElementById('canchas-table-body');
            tbody.innerHTML = '';
            
            canchasFiltradas.forEach(cancha => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div>
                            <p class="font-medium">${cancha.nombre}</p>
                            <p class="text-sm text-gris-600">${cancha.direccion}</p>
                        </div>
                    </td>
                    <td>${formatearDeporte(cancha.deporte)}</td>
                    <td>${cancha.comuna}</td>
                    <td>${cancha.precioFormateado || formatearPrecio(cancha.precioHoraCLP)}</td>
                    <td>
                        <div class="chip chip-${cancha.disponibilidad || 'disponible'}">
                            ${cancha.disponibilidad === 'disponible' ? 'Activa' : 
                              cancha.disponibilidad === 'pocas' ? 'Pocas' : 'Inactiva'}
                        </div>
                    </td>
                    <td>
                        <div class="flex items-center gap-1">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="var(--rojo)" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                            </svg>
                            <span class="text-sm">${cancha.rating}</span>
                        </div>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-outline btn-sm" onclick="editarCancha(${cancha.id})">Editar</button>
                            <button class="btn btn-outline btn-sm text-rojo" onclick="eliminarCancha(${cancha.id})">Eliminar</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function aplicarFiltros() {
            const deporte = document.getElementById('filter-deporte').value;
            const estado = document.getElementById('filter-estado').value;
            const buscar = document.getElementById('filter-buscar').value.toLowerCase();
            
            canchasFiltradas = canchas.filter(cancha => {
                const cumpleDeporte = !deporte || cancha.deporte === deporte;
                const cumpleEstado = !estado || cancha.disponibilidad === estado;
                const cumpleBuscar = !buscar || 
                    cancha.nombre.toLowerCase().includes(buscar) ||
                    cancha.direccion.toLowerCase().includes(buscar) ||
                    cancha.comuna.toLowerCase().includes(buscar);
                
                return cumpleDeporte && cumpleEstado && cumpleBuscar;
            });
            
            renderCanchasTable();
            actualizarContador();
        }

        function limpiarFiltros() {
            document.getElementById('filter-deporte').value = '';
            document.getElementById('filter-estado').value = '';
            document.getElementById('filter-buscar').value = '';
            aplicarFiltros();
        }

        function actualizarContador() {
            const count = canchasFiltradas.length;
            document.getElementById('canchas-count').textContent = `${count} cancha${count !== 1 ? 's' : ''}`;
        }

        function abrirModalCancha(cancha = null) {
            const modal = document.getElementById('modal-cancha-backdrop');
            const title = document.getElementById('modal-cancha-title');
            const form = document.getElementById('cancha-form');
            
            if (cancha) {
                title.textContent = 'Editar Cancha';
                llenarFormularioCancha(cancha);
            } else {
                title.textContent = 'Nueva Cancha';
                form.reset();
                document.getElementById('cancha-id').value = '';
            }
            
            modal.classList.add('active');
            document.getElementById('modal-cancha').classList.add('active');
        }

        function cerrarModalCancha() {
            const modal = document.getElementById('modal-cancha-backdrop');
            modal.classList.remove('active');
            document.getElementById('modal-cancha').classList.remove('active');
        }

        function llenarFormularioCancha(cancha) {
            document.getElementById('cancha-id').value = cancha.id;
            document.getElementById('cancha-nombre').value = cancha.nombre;
            document.getElementById('cancha-deporte').value = cancha.deporte;
            document.getElementById('cancha-superficie').value = cancha.superficie;
            document.getElementById('cancha-direccion').value = cancha.direccion;
            document.getElementById('cancha-comuna').value = cancha.comuna;
            document.getElementById('cancha-precio').value = cancha.precioHoraCLP;
            document.getElementById('cancha-descripcion').value = cancha.descripcion || '';
            document.getElementById('cancha-telefono').value = cancha.telefono || '';
            document.getElementById('cancha-email').value = cancha.email || '';
            
            // Marcar servicios
            if (cancha.servicios) {
                cancha.servicios.forEach(servicio => {
                    const checkbox = document.querySelector(`input[name="servicios"][value="${servicio}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            }
        }

        function guardarCancha(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const servicios = Array.from(document.querySelectorAll('input[name="servicios"]:checked')).map(cb => cb.value);
            
            const canchaData = {
                id: formData.get('id') || null,
                nombre: formData.get('nombre'),
                deporte: formData.get('deporte'),
                superficie: formData.get('superficie'),
                direccion: formData.get('direccion'),
                comuna: formData.get('comuna'),
                precioHoraCLP: parseInt(formData.get('precioHoraCLP')),
                descripcion: formData.get('descripcion'),
                telefono: formData.get('telefono'),
                email: formData.get('email'),
                servicios: servicios
            };
            
            // Simular guardado (en producción se enviaría a la API)
            console.log('Guardando cancha:', canchaData);
            
            // Mostrar mensaje de éxito
            alert(canchaData.id ? 'Cancha actualizada exitosamente' : 'Cancha creada exitosamente');
            
            cerrarModalCancha();
            cargarCanchas(); // Recargar lista
        }

        function editarCancha(id) {
            const cancha = canchas.find(c => c.id === id);
            if (cancha) {
                abrirModalCancha(cancha);
            }
        }

        function eliminarCancha(id) {
            const cancha = canchas.find(c => c.id === id);
            if (cancha && confirm(`¿Estás seguro de eliminar la cancha "${cancha.nombre}"?`)) {
                // Simular eliminación
                console.log('Eliminando cancha:', id);
                alert('Cancha eliminada exitosamente');
                cargarCanchas(); // Recargar lista
            }
        }

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('usuario');
            window.location.href = '../index.html';
        }

        // Funciones auxiliares
        function formatearDeporte(deporte) {
            const deportes = {
                'futbol': 'Fútbol',
                'tenis': 'Tenis',
                'padel': 'Pádel',
                'basquet': 'Básquetbol',
                'baby_futbol': 'Baby Fútbol'
            };
            return deportes[deporte] || deporte;
        }

        function formatearPrecio(precio) {
            return '$' + precio.toLocaleString('es-CL');
        }
    </script>
</body>
</html>
