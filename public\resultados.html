<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resultados de Búsqueda - CanchaCL</title>
    <meta name="description" content="Resultados de búsqueda de canchas deportivas en Chile. Encuentra la cancha perfecta para tu deporte.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="#" id="nav-login">Iniciar Sesión</a></li>
                            <li><a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="#" id="nav-login-mobile" class="btn btn-ghost btn-sm">Entrar</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="perfil.html">Mi Perfil</a></li>
                <li><a href="admin/dashboard.html">Panel Admin</a></li>
                <li><a href="#" id="logout-link">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Filters Section -->
        <section class="filters-section">
            <div class="container">
                <div class="filters-toggle">
                    <button class="btn btn-outline" id="filters-toggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 3H2L10 12.46V19L14 21V12.46L22 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Filtros
                    </button>
                </div>
                
                <div class="filters-content active" id="filters-content">
                    <form class="filters-grid" id="filters-form">
                        <div class="form-group">
                            <label for="filter-deporte" class="form-label">Deporte</label>
                            <select id="filter-deporte" name="deporte" class="form-input form-select">
                                <option value="">Todos los deportes</option>
                                <option value="futbol">Fútbol</option>
                                <option value="tenis">Tenis</option>
                                <option value="padel">Pádel</option>
                                <option value="basquet">Básquetbol</option>
                                <option value="baby_futbol">Baby Fútbol</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="filter-comuna" class="form-label">Comuna</label>
                            <select id="filter-comuna" name="comuna" class="form-input form-select">
                                <option value="">Todas las comunas</option>
                                <!-- Se llenan dinámicamente -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="filter-precio-min" class="form-label">Precio mínimo</label>
                            <select id="filter-precio-min" name="precio_min" class="form-input form-select">
                                <option value="">Sin mínimo</option>
                                <option value="10000">$10.000</option>
                                <option value="15000">$15.000</option>
                                <option value="20000">$20.000</option>
                                <option value="25000">$25.000</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="filter-precio-max" class="form-label">Precio máximo</label>
                            <select id="filter-precio-max" name="precio_max" class="form-input form-select">
                                <option value="">Sin máximo</option>
                                <option value="15000">$15.000</option>
                                <option value="20000">$20.000</option>
                                <option value="25000">$25.000</option>
                                <option value="30000">$30.000</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="filter-superficie" class="form-label">Superficie</label>
                            <select id="filter-superficie" name="superficie" class="form-input form-select">
                                <option value="">Todas las superficies</option>
                                <option value="natural">Pasto natural</option>
                                <option value="sintético">Pasto sintético</option>
                                <option value="arcilla">Arcilla</option>
                                <option value="cemento">Cemento</option>
                                <option value="parquet">Parquet</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="filter-disponibilidad" class="form-label">Disponibilidad</label>
                            <select id="filter-disponibilidad" name="disponibilidad" class="form-input form-select">
                                <option value="">Todas</option>
                                <option value="disponible">Disponible</option>
                                <option value="pocas">Pocas disponibles</option>
                            </select>
                        </div>
                        
                        <div class="flex gap-2">
                            <button type="submit" class="btn btn-primary">Aplicar filtros</button>
                            <button type="button" class="btn btn-outline" id="clear-filters">Limpiar</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section class="section">
            <div class="container">
                <!-- Results Header -->
                <div class="results-header">
                    <div class="results-count" id="results-count">
                        Cargando canchas...
                    </div>
                    
                    <div class="form-group mb-0">
                        <label for="sort-select" class="form-label">Ordenar por:</label>
                        <select id="sort-select" class="form-input form-select sort-select">
                            <option value="rating_desc">Mejor valoradas</option>
                            <option value="precio_asc">Precio: menor a mayor</option>
                            <option value="precio_desc">Precio: mayor a menor</option>
                            <option value="nombre_asc">Nombre A-Z</option>
                        </select>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loading-state" class="text-center" style="padding: var(--espacio-3xl) 0;">
                    <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                    <p>Buscando canchas...</p>
                </div>

                <!-- Results Grid -->
                <div class="results-grid" id="results-grid" style="display: none;">
                    <!-- Las canchas se cargan dinámicamente aquí -->
                </div>

                <!-- No Results -->
                <div id="no-results" class="no-results" style="display: none;">
                    <div class="no-results-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="11" cy="11" r="8" stroke="var(--gris-400)" stroke-width="2"/>
                            <path d="M21 21L16.65 16.65" stroke="var(--gris-400)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11H14M11 8V14" stroke="var(--gris-400)" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <h3>No encontramos canchas</h3>
                    <p>No hay canchas que coincidan con tus criterios de búsqueda. Prueba ajustando los filtros.</p>
                    <button class="btn btn-primary" id="reset-search">Buscar todas las canchas</button>
                </div>

                <!-- Pagination -->
                <div id="pagination" class="flex justify-center gap-2" style="margin-top: var(--espacio-xl); display: none;">
                    <!-- Los botones de paginación se generan dinámicamente -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        // Inicializar página de resultados
        document.addEventListener('DOMContentLoaded', function() {
            // Cargar comunas para filtros
            if (window.API) {
                window.API.getComunas().then(response => {
                    if (response.success) {
                        const comunaSelect = document.getElementById('filter-comuna');
                        response.data.forEach(comuna => {
                            const option = document.createElement('option');
                            option.value = comuna.nombre;
                            option.textContent = comuna.nombre;
                            comunaSelect.appendChild(option);
                        });
                    }
                }).catch(console.error);
            }

            // Obtener parámetros de URL y aplicar filtros
            const urlParams = new URLSearchParams(window.location.search);
            const filtros = {};
            
            // Aplicar filtros desde URL
            ['deporte', 'comuna', 'precio_min', 'precio_max', 'superficie', 'disponibilidad'].forEach(param => {
                const valor = urlParams.get(param);
                if (valor) {
                    filtros[param] = valor;
                    const input = document.getElementById(`filter-${param}`);
                    if (input) input.value = valor;
                }
            });

            // Cargar resultados iniciales
            cargarCanchas(filtros);
        });

        // Función para cargar canchas
        function cargarCanchas(filtros = {}) {
            const loadingState = document.getElementById('loading-state');
            const resultsGrid = document.getElementById('results-grid');
            const noResults = document.getElementById('no-results');
            const resultsCount = document.getElementById('results-count');

            // Mostrar estado de carga
            loadingState.style.display = 'block';
            resultsGrid.style.display = 'none';
            noResults.style.display = 'none';

            // Agregar ordenamiento
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect.value) {
                filtros.orden = sortSelect.value;
            }

            if (window.API) {
                window.API.getCanchas(filtros).then(response => {
                    loadingState.style.display = 'none';
                    
                    if (response.success && response.data.length > 0) {
                        resultsGrid.style.display = 'grid';
                        resultsCount.textContent = `${response.pagination.total} canchas encontradas`;
                        
                        // Renderizar canchas
                        if (window.UI && window.UI.renderCanchaCards) {
                            window.UI.renderCanchaCards(response.data, 'results-grid');
                        }
                        
                        // Renderizar paginación si es necesario
                        if (response.pagination.total_paginas > 1) {
                            renderPagination(response.pagination);
                        }
                    } else {
                        noResults.style.display = 'block';
                        resultsCount.textContent = '0 canchas encontradas';
                    }
                }).catch(error => {
                    console.error('Error cargando canchas:', error);
                    loadingState.style.display = 'none';
                    noResults.style.display = 'block';
                    resultsCount.textContent = 'Error al cargar canchas';
                });
            }
        }

        // Función para renderizar paginación
        function renderPagination(pagination) {
            const paginationContainer = document.getElementById('pagination');
            paginationContainer.innerHTML = '';
            paginationContainer.style.display = 'flex';

            for (let i = 1; i <= pagination.total_paginas; i++) {
                const button = document.createElement('button');
                button.className = `btn ${i === pagination.pagina ? 'btn-primary' : 'btn-outline'}`;
                button.textContent = i;
                button.onclick = () => {
                    const filtros = obtenerFiltrosActuales();
                    filtros.pagina = i;
                    cargarCanchas(filtros);
                };
                paginationContainer.appendChild(button);
            }
        }

        // Función para obtener filtros actuales
        function obtenerFiltrosActuales() {
            const form = document.getElementById('filters-form');
            const formData = new FormData(form);
            const filtros = {};
            
            for (const [key, value] of formData.entries()) {
                if (value) filtros[key] = value;
            }
            
            return filtros;
        }
    </script>
</body>
</html>
