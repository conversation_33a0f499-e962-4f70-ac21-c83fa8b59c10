<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Función para generar ID único mock
function generarIdMock() {
    return rand(1000, 9999);
}

// Función para generar token mock
function generarTokenMock($userId) {
    return base64_encode(json_encode([
        'userId' => $userId,
        'timestamp' => time(),
        'expires' => time() + (24 * 60 * 60) // 24 horas
    ]));
}

// Función para validar email
function validarEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Función para validar teléfono chileno
function validarTelefonoChileno($telefono) {
    // Formato: +56 9 XXXX XXXX o variaciones
    $patron = '/^\+56\s?[0-9]\s?[0-9]{4}\s?[0-9]{4}$/';
    return preg_match($patron, $telefono);
}

// Emails ya registrados (simulación)
$emailsRegistrados = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // Obtener datos del cuerpo de la petición
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Datos inválidos'
            ]);
            exit();
        }
        
        $nombre = trim($input['nombre'] ?? '');
        $apellido = trim($input['apellido'] ?? '');
        $email = trim($input['email'] ?? '');
        $telefono = trim($input['telefono'] ?? '');
        $password = $input['password'] ?? '';
        $confirmPassword = $input['confirmPassword'] ?? '';
        $tipo = $input['tipo'] ?? 'usuario'; // usuario, dueno
        $aceptaTerminos = $input['aceptaTerminos'] ?? false;
        
        // Validaciones
        $errores = [];
        
        if (empty($nombre)) {
            $errores[] = 'El nombre es requerido';
        } elseif (strlen($nombre) < 2) {
            $errores[] = 'El nombre debe tener al menos 2 caracteres';
        }
        
        if (empty($apellido)) {
            $errores[] = 'El apellido es requerido';
        } elseif (strlen($apellido) < 2) {
            $errores[] = 'El apellido debe tener al menos 2 caracteres';
        }
        
        if (empty($email)) {
            $errores[] = 'El email es requerido';
        } elseif (!validarEmail($email)) {
            $errores[] = 'El email no tiene un formato válido';
        } elseif (in_array(strtolower($email), array_map('strtolower', $emailsRegistrados))) {
            $errores[] = 'Este email ya está registrado';
        }
        
        if (empty($telefono)) {
            $errores[] = 'El teléfono es requerido';
        } elseif (!validarTelefonoChileno($telefono)) {
            $errores[] = 'El teléfono debe tener formato chileno (+56 9 XXXX XXXX)';
        }
        
        if (empty($password)) {
            $errores[] = 'La contraseña es requerida';
        } elseif (strlen($password) < 6) {
            $errores[] = 'La contraseña debe tener al menos 6 caracteres';
        }
        
        if ($password !== $confirmPassword) {
            $errores[] = 'Las contraseñas no coinciden';
        }
        
        if (!in_array($tipo, ['usuario', 'dueno'])) {
            $errores[] = 'Tipo de usuario inválido';
        }
        
        if (!$aceptaTerminos) {
            $errores[] = 'Debe aceptar los términos y condiciones';
        }
        
        if (!empty($errores)) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Errores de validación',
                'errores' => $errores
            ]);
            exit();
        }
        
        // Crear usuario mock
        $nuevoUsuario = [
            'id' => generarIdMock(),
            'nombre' => $nombre,
            'apellido' => $apellido,
            'email' => $email,
            'telefono' => $telefono,
            'tipo' => $tipo,
            'fechaRegistro' => date('Y-m-d'),
            'activo' => true
        ];
        
        // Generar token
        $token = generarTokenMock($nuevoUsuario['id']);
        
        // Respuesta exitosa
        echo json_encode([
            'success' => true,
            'message' => 'Registro exitoso. ¡Bienvenido a CanchaCL!',
            'data' => [
                'token' => $token,
                'usuario' => $nuevoUsuario
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Método no permitido'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
