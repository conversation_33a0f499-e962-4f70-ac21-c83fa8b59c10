<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Usuarios mock para testing
$usuariosMock = [
    [
        'id' => 1,
        'email' => '<EMAIL>',
        'password' => '123456', // En producción esto debería estar hasheado
        'nombre' => 'Juan',
        'apellido' => '<PERSON>',
        'telefono' => '+56 9 8765 4321',
        'tipo' => 'usuario',
        'fechaRegistro' => '2024-01-15'
    ],
    [
        'id' => 2,
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'nombre' => 'María',
        'apellido' => '<PERSON>',
        'telefono' => '+56 9 1234 5678',
        'tipo' => 'admin',
        'fechaRegistro' => '2024-01-01'
    ],
    [
        'id' => 3,
        'email' => '<EMAIL>',
        'password' => 'dueno123',
        'nombre' => 'Carlos',
        'apellido' => 'Silva',
        'telefono' => '+56 9 9876 5432',
        'tipo' => 'dueno',
        'fechaRegistro' => '2024-02-01'
    ]
];

// Función para generar token mock
function generarTokenMock($userId) {
    return base64_encode(json_encode([
        'userId' => $userId,
        'timestamp' => time(),
        'expires' => time() + (24 * 60 * 60) // 24 horas
    ]));
}

// Función para validar email
function validarEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // Obtener datos del cuerpo de la petición
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Datos inválidos'
            ]);
            exit();
        }
        
        $email = trim($input['email'] ?? '');
        $password = $input['password'] ?? '';
        
        // Validaciones
        $errores = [];
        
        if (empty($email)) {
            $errores[] = 'El email es requerido';
        } elseif (!validarEmail($email)) {
            $errores[] = 'El email no tiene un formato válido';
        }
        
        if (empty($password)) {
            $errores[] = 'La contraseña es requerida';
        }
        
        if (!empty($errores)) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Errores de validación',
                'errores' => $errores
            ]);
            exit();
        }
        
        // Buscar usuario
        $usuario = null;
        foreach ($usuariosMock as $u) {
            if (strtolower($u['email']) === strtolower($email)) {
                $usuario = $u;
                break;
            }
        }
        
        // Verificar credenciales
        if (!$usuario || $usuario['password'] !== $password) {
            http_response_code(401);
            echo json_encode([
                'error' => true,
                'message' => 'Credenciales inválidas'
            ]);
            exit();
        }
        
        // Generar token
        $token = generarTokenMock($usuario['id']);
        
        // Remover password de la respuesta
        unset($usuario['password']);
        
        // Respuesta exitosa
        echo json_encode([
            'success' => true,
            'message' => 'Inicio de sesión exitoso',
            'data' => [
                'token' => $token,
                'usuario' => $usuario
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Método no permitido'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
