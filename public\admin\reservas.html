<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Reservas - CanchaCL Admin</title>
    <meta name="description" content="Gestiona las reservas de tus canchas deportivas: ver, confirmar, cancelar y administrar pagos.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../../assets/css/base.css">
    <link rel="stylesheet" href="../../assets/css/layout.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    Cancha<span class="accent">CL</span> <span class="text-sm text-gris-600">Admin</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="dashboard.html">Dashboard</a></li>
                            <li><a href="canchas.html">Canchas</a></li>
                            <li><a href="reservas.html">Reservas</a></li>
                            <li><a href="../perfil.html">Mi Perfil</a></li>
                            <li><a href="#" id="logout-desktop">Cerrar Sesión</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <span class="text-sm" id="admin-name-mobile">Admin</span>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="canchas.html">Canchas</a></li>
                <li><a href="reservas.html">Reservas</a></li>
                <li><a href="../perfil.html">Mi Perfil</a></li>
                <li><a href="../index.html">Ver sitio público</a></li>
                <li><a href="#" id="logout-mobile">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Page Header -->
        <section class="section" style="background-color: var(--gris-100); padding-top: var(--espacio-lg); padding-bottom: var(--espacio-lg);">
            <div class="container">
                <div class="admin-header">
                    <div>
                        <h1>Gestión de Reservas</h1>
                        <p class="text-gris-600">Administra las reservas de tus canchas</p>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-outline" id="btn-exportar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Exportar
                        </button>
                        <button class="btn btn-primary" id="btn-nueva-reserva">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Nueva Reserva
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Cards -->
        <section class="section" style="padding-top: var(--espacio-lg); padding-bottom: 0;">
            <div class="container">
                <div class="admin-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="reservas-hoy">-</div>
                        <div class="stat-label">Reservas hoy</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="reservas-semana">-</div>
                        <div class="stat-label">Esta semana</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="ingresos-dia">-</div>
                        <div class="stat-label">Ingresos hoy</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="pendientes">-</div>
                        <div class="stat-label">Pendientes</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reservas List -->
        <section class="section">
            <div class="container">
                <!-- Filters -->
                <div class="card" style="margin-bottom: var(--espacio-xl);">
                    <div class="card-body">
                        <div class="grid grid-cols-1 grid-cols-md-5 gap-4">
                            <div class="form-group mb-0">
                                <label for="filter-fecha" class="form-label">Fecha</label>
                                <input type="date" id="filter-fecha" class="form-input">
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="filter-cancha" class="form-label">Cancha</label>
                                <select id="filter-cancha" class="form-input form-select">
                                    <option value="">Todas las canchas</option>
                                    <!-- Se llenan dinámicamente -->
                                </select>
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="filter-estado" class="form-label">Estado</label>
                                <select id="filter-estado" class="form-input form-select">
                                    <option value="">Todos los estados</option>
                                    <option value="confirmada">Confirmada</option>
                                    <option value="pendiente">Pendiente</option>
                                    <option value="cancelada">Cancelada</option>
                                    <option value="completada">Completada</option>
                                </select>
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="filter-buscar" class="form-label">Buscar</label>
                                <input type="text" id="filter-buscar" class="form-input" placeholder="Cliente o reserva">
                            </div>
                            
                            <div class="flex items-end">
                                <button class="btn btn-outline" id="btn-limpiar-filtros">Limpiar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reservas Table -->
                <div class="card">
                    <div class="card-header">
                        <h3>Reservas</h3>
                        <span class="text-sm text-gris-600" id="reservas-count">0 reservas</span>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <!-- Loading state -->
                        <div id="reservas-loading" class="text-center" style="padding: var(--espacio-xl) 0;">
                            <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                            <p>Cargando reservas...</p>
                        </div>
                        
                        <!-- Table -->
                        <div id="reservas-table-container" style="display: none; overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Reserva</th>
                                        <th>Cliente</th>
                                        <th>Cancha</th>
                                        <th>Fecha y Hora</th>
                                        <th>Duración</th>
                                        <th>Total</th>
                                        <th>Estado</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="reservas-table-body">
                                    <!-- Se llena dinámicamente -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- No reservas -->
                        <div id="no-reservas" class="text-center" style="padding: var(--espacio-xl) 0; display: none;">
                            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-lg); opacity: 0.5;">
                                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="var(--gris-400)" stroke-width="2"/>
                                    <line x1="16" y1="2" x2="16" y2="6" stroke="var(--gris-400)" stroke-width="2"/>
                                    <line x1="8" y1="2" x2="8" y2="6" stroke="var(--gris-400)" stroke-width="2"/>
                                    <line x1="3" y1="10" x2="21" y2="10" stroke="var(--gris-400)" stroke-width="2"/>
                                </svg>
                            </div>
                            <h3>No hay reservas</h3>
                            <p>Las reservas aparecerán aquí cuando los usuarios reserven tus canchas.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal Detalle Reserva -->
    <div class="modal-backdrop" id="modal-reserva-backdrop">
        <div class="modal" id="modal-reserva">
            <div class="modal-header">
                <h3 class="modal-title">Detalle de Reserva</h3>
                <button class="modal-close" id="modal-reserva-close">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div id="reserva-detalle-content">
                    <!-- Se llena dinámicamente -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-reserva-cerrar">Cerrar</button>
                <button type="button" class="btn btn-primary" id="modal-reserva-accion" style="display: none;">Confirmar</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL Admin</h3>
                    <p>Panel de administración para dueños de canchas deportivas.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Soporte</h3>
                    <ul>
                        <li><a href="#">Centro de Ayuda</a></li>
                        <li><a href="#">Contactar Soporte</a></li>
                        <li><a href="#">Guías y Tutoriales</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos de Servicio</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Comisiones y Tarifas</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Panel Administrativo.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../assets/js/app.js" defer></script>
    <script src="../../assets/js/api.js" defer></script>
    <script src="../../assets/js/ui.js" defer></script>
    <script>
        let usuario = null;
        let reservas = [];
        let reservasFiltradas = [];
        let canchas = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Verificar autenticación y permisos
            const token = localStorage.getItem('token');
            const usuarioData = localStorage.getItem('usuario');
            
            if (!token || !usuarioData) {
                window.location.href = '../login.html?redirect=admin/reservas.html';
                return;
            }
            
            try {
                usuario = JSON.parse(usuarioData);
                
                if (usuario.tipo !== 'admin' && usuario.tipo !== 'dueno') {
                    alert('No tienes permisos para acceder al panel administrativo.');
                    window.location.href = '../perfil.html';
                    return;
                }
                
                inicializarPagina();
                cargarCanchas();
                cargarReservas();
                cargarEstadisticas();
                
            } catch (error) {
                console.error('Error parsing user data:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('usuario');
                window.location.href = '../login.html';
            }
        });

        function inicializarPagina() {
            // Mostrar nombre del admin
            document.getElementById('admin-name-mobile').textContent = usuario.nombre;
            
            // Configurar event listeners
            document.getElementById('btn-nueva-reserva').addEventListener('click', () => {
                alert('Funcionalidad de nueva reserva manual en desarrollo');
            });
            document.getElementById('btn-exportar').addEventListener('click', exportarReservas);
            document.getElementById('modal-reserva-close').addEventListener('click', cerrarModalReserva);
            document.getElementById('modal-reserva-cerrar').addEventListener('click', cerrarModalReserva);
            document.getElementById('modal-reserva-backdrop').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) cerrarModalReserva();
            });
            
            // Configurar filtros
            document.getElementById('filter-fecha').addEventListener('change', aplicarFiltros);
            document.getElementById('filter-cancha').addEventListener('change', aplicarFiltros);
            document.getElementById('filter-estado').addEventListener('change', aplicarFiltros);
            document.getElementById('filter-buscar').addEventListener('input', aplicarFiltros);
            document.getElementById('btn-limpiar-filtros').addEventListener('click', limpiarFiltros);
            
            // Configurar logout
            document.getElementById('logout-desktop').addEventListener('click', logout);
            document.getElementById('logout-mobile').addEventListener('click', logout);
            
            // Establecer fecha de hoy por defecto
            const hoy = new Date().toISOString().split('T')[0];
            document.getElementById('filter-fecha').value = hoy;
        }

        function cargarCanchas() {
            if (window.API) {
                window.API.getCanchas().then(response => {
                    if (response.success) {
                        canchas = response.data;
                        const canchaSelect = document.getElementById('filter-cancha');
                        response.data.forEach(cancha => {
                            const option = document.createElement('option');
                            option.value = cancha.id;
                            option.textContent = cancha.nombre;
                            canchaSelect.appendChild(option);
                        });
                    }
                }).catch(console.error);
            }
        }

        function cargarReservas() {
            const loadingElement = document.getElementById('reservas-loading');
            const tableContainer = document.getElementById('reservas-table-container');
            const noReservasElement = document.getElementById('no-reservas');
            
            loadingElement.style.display = 'block';
            tableContainer.style.display = 'none';
            noReservasElement.style.display = 'none';
            
            if (window.API) {
                window.API.getReservas().then(response => {
                    loadingElement.style.display = 'none';
                    
                    if (response.success && response.data.length > 0) {
                        reservas = response.data;
                        reservasFiltradas = [...reservas];
                        tableContainer.style.display = 'block';
                        aplicarFiltros(); // Aplicar filtros iniciales
                    } else {
                        noReservasElement.style.display = 'block';
                    }
                }).catch(error => {
                    console.error('Error cargando reservas:', error);
                    loadingElement.style.display = 'none';
                    noReservasElement.style.display = 'block';
                });
            }
        }

        function cargarEstadisticas() {
            // Simular estadísticas (en producción vendría de la API)
            setTimeout(() => {
                document.getElementById('reservas-hoy').textContent = '8';
                document.getElementById('reservas-semana').textContent = '47';
                document.getElementById('ingresos-dia').textContent = '$156.000';
                document.getElementById('pendientes').textContent = '3';
            }, 500);
        }

        function renderReservasTable() {
            const tbody = document.getElementById('reservas-table-body');
            tbody.innerHTML = '';
            
            reservasFiltradas.forEach(reserva => {
                const row = document.createElement('tr');
                
                const estadoClass = reserva.estado === 'confirmada' ? 'chip-disponible' : 
                                   reserva.estado === 'pendiente' ? 'chip-pocas' : 'chip-agotado';
                
                row.innerHTML = `
                    <td>
                        <div>
                            <p class="font-medium">#${reserva.id}</p>
                            <p class="text-sm text-gris-600">${formatearFechaHora(reserva.fechaReserva)}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="font-medium">Usuario ${reserva.usuarioId}</p>
                            <p class="text-sm text-gris-600">ID: ${reserva.usuarioId}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="font-medium">${reserva.cancha?.nombre || 'Cancha'}</p>
                            <p class="text-sm text-gris-600">${reserva.cancha?.comuna || ''}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="font-medium">${formatearFecha(reserva.fecha)}</p>
                            <p class="text-sm text-gris-600">${reserva.hora}</p>
                        </div>
                    </td>
                    <td>${reserva.duracion} hora${reserva.duracion > 1 ? 's' : ''}</td>
                    <td class="font-medium">${reserva.totalFormateado}</td>
                    <td>
                        <div class="chip ${estadoClass}">
                            ${reserva.estado}
                        </div>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-outline btn-sm" onclick="verDetalleReserva(${reserva.id})">Ver</button>
                            ${reserva.estado === 'pendiente' ? 
                                '<button class="btn btn-primary btn-sm" onclick="confirmarReserva(' + reserva.id + ')">Confirmar</button>' : ''}
                            ${reserva.estado !== 'cancelada' ? 
                                '<button class="btn btn-outline btn-sm text-rojo" onclick="cancelarReserva(' + reserva.id + ')">Cancelar</button>' : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function aplicarFiltros() {
            const fecha = document.getElementById('filter-fecha').value;
            const canchaId = document.getElementById('filter-cancha').value;
            const estado = document.getElementById('filter-estado').value;
            const buscar = document.getElementById('filter-buscar').value.toLowerCase();
            
            reservasFiltradas = reservas.filter(reserva => {
                const cumpleFecha = !fecha || reserva.fecha === fecha;
                const cumpleCancha = !canchaId || reserva.canchaId == canchaId;
                const cumpleEstado = !estado || reserva.estado === estado;
                const cumpleBuscar = !buscar || 
                    reserva.id.toString().includes(buscar) ||
                    (reserva.cancha?.nombre || '').toLowerCase().includes(buscar);
                
                return cumpleFecha && cumpleCancha && cumpleEstado && cumpleBuscar;
            });
            
            renderReservasTable();
            actualizarContador();
        }

        function limpiarFiltros() {
            document.getElementById('filter-fecha').value = '';
            document.getElementById('filter-cancha').value = '';
            document.getElementById('filter-estado').value = '';
            document.getElementById('filter-buscar').value = '';
            aplicarFiltros();
        }

        function actualizarContador() {
            const count = reservasFiltradas.length;
            document.getElementById('reservas-count').textContent = `${count} reserva${count !== 1 ? 's' : ''}`;
        }

        function verDetalleReserva(id) {
            const reserva = reservas.find(r => r.id === id);
            if (!reserva) return;
            
            const content = document.getElementById('reserva-detalle-content');
            content.innerHTML = `
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <h4>Información de la Reserva</h4>
                        <div class="resumen-reserva">
                            <div class="resumen-item">
                                <span>Número de reserva:</span>
                                <span>#${reserva.id}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Estado:</span>
                                <span class="chip chip-${reserva.estado === 'confirmada' ? 'disponible' : 'pocas'}">${reserva.estado}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Fecha de reserva:</span>
                                <span>${formatearFechaHora(reserva.fechaReserva)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4>Detalles del Evento</h4>
                        <div class="resumen-reserva">
                            <div class="resumen-item">
                                <span>Cancha:</span>
                                <span>${reserva.cancha?.nombre || 'Cancha'}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Fecha:</span>
                                <span>${formatearFecha(reserva.fecha)}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Hora:</span>
                                <span>${reserva.hora}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Duración:</span>
                                <span>${reserva.duracion} hora${reserva.duracion > 1 ? 's' : ''}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4>Información de Pago</h4>
                        <div class="resumen-reserva">
                            <div class="resumen-item">
                                <span>Método de pago:</span>
                                <span>${formatearMetodoPago(reserva.metodoPago)}</span>
                            </div>
                            <div class="resumen-item">
                                <span>Total:</span>
                                <span class="font-semibold">${reserva.totalFormateado}</span>
                            </div>
                        </div>
                    </div>
                    
                    ${reserva.notas ? `
                    <div>
                        <h4>Notas</h4>
                        <p class="text-sm text-gris-600">${reserva.notas}</p>
                    </div>
                    ` : ''}
                </div>
            `;
            
            // Configurar botón de acción
            const btnAccion = document.getElementById('modal-reserva-accion');
            if (reserva.estado === 'pendiente') {
                btnAccion.style.display = 'block';
                btnAccion.textContent = 'Confirmar Reserva';
                btnAccion.onclick = () => {
                    confirmarReserva(reserva.id);
                    cerrarModalReserva();
                };
            } else {
                btnAccion.style.display = 'none';
            }
            
            abrirModalReserva();
        }

        function abrirModalReserva() {
            const modal = document.getElementById('modal-reserva-backdrop');
            modal.classList.add('active');
            document.getElementById('modal-reserva').classList.add('active');
        }

        function cerrarModalReserva() {
            const modal = document.getElementById('modal-reserva-backdrop');
            modal.classList.remove('active');
            document.getElementById('modal-reserva').classList.remove('active');
        }

        function confirmarReserva(id) {
            if (confirm('¿Confirmar esta reserva?')) {
                // Simular confirmación
                console.log('Confirmando reserva:', id);
                alert('Reserva confirmada exitosamente');
                cargarReservas(); // Recargar lista
            }
        }

        function cancelarReserva(id) {
            if (confirm('¿Estás seguro de cancelar esta reserva?')) {
                // Simular cancelación
                console.log('Cancelando reserva:', id);
                alert('Reserva cancelada exitosamente');
                cargarReservas(); // Recargar lista
            }
        }

        function exportarReservas() {
            // Simular exportación
            alert('Exportando reservas a CSV...\n(Funcionalidad en desarrollo)');
        }

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('usuario');
            window.location.href = '../index.html';
        }

        // Funciones auxiliares
        function formatearFecha(fecha) {
            const date = new Date(fecha + 'T00:00:00');
            return date.toLocaleDateString('es-CL', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
            });
        }

        function formatearFechaHora(fechaHora) {
            const date = new Date(fechaHora);
            return date.toLocaleDateString('es-CL', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatearMetodoPago(metodo) {
            const metodos = {
                'tarjeta': 'Tarjeta de crédito/débito',
                'transferencia': 'Transferencia bancaria',
                'efectivo': 'Efectivo en el lugar'
            };
            return metodos[metodo] || metodo;
        }
    </script>
</body>
</html>
