<?php
/**
 * CanchaCL - Sistema de Autenticación JWT
 * Manejo de tokens JWT y autenticación de usuarios
 */

require_once 'config.php';

/**
 * Clase para manejo de JWT (implementación simple)
 */
class JWT {
    
    /**
     * Codifica un payload en JWT
     */
    public static function encode($payload, $key = JWT_SECRET) {
        $header = json_encode(['typ' => 'JWT', 'alg' => JWT_ALGORITHM]);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $key, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Decodifica un JWT
     */
    public static function decode($jwt, $key = JWT_SECRET) {
        $parts = explode('.', $jwt);
        
        if (count($parts) !== 3) {
            throw new Exception('Token JWT inválido');
        }
        
        list($base64Header, $base64Payload, $base64Signature) = $parts;
        
        $header = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Header)), true);
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)), true);
        
        if (!$header || !$payload) {
            throw new Exception('Token JWT inválido');
        }
        
        // Verificar algoritmo
        if ($header['alg'] !== JWT_ALGORITHM) {
            throw new Exception('Algoritmo JWT no soportado');
        }
        
        // Verificar firma
        $signature = base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Signature));
        $expectedSignature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $key, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            throw new Exception('Firma JWT inválida');
        }
        
        // Verificar expiración
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            throw new Exception('Token JWT expirado');
        }
        
        return $payload;
    }
}

/**
 * Clase para manejo de autenticación
 */
class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Genera un token JWT para un usuario
     */
    public function generateToken($user) {
        $payload = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'tipo' => $user['tipo'],
            'iat' => time(),
            'exp' => time() + JWT_EXPIRATION
        ];
        
        return JWT::encode($payload);
    }
    
    /**
     * Valida un token JWT
     */
    public function validateToken($token) {
        try {
            return JWT::decode($token);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Obtiene el token del header Authorization
     */
    public function getTokenFromHeader() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Middleware para verificar autenticación
     */
    public function requireAuth() {
        $token = $this->getTokenFromHeader();
        
        if (!$token) {
            JsonResponse::unauthorized('Token de acceso requerido');
        }
        
        $payload = $this->validateToken($token);
        
        if (!$payload) {
            JsonResponse::unauthorized('Token de acceso inválido o expirado');
        }
        
        // Verificar que el usuario aún existe
        $user = $this->getUserById($payload['user_id']);
        if (!$user) {
            JsonResponse::unauthorized('Usuario no encontrado');
        }
        
        return $payload;
    }
    
    /**
     * Middleware para verificar roles específicos
     */
    public function requireRole($allowedRoles) {
        $payload = $this->requireAuth();
        
        if (!in_array($payload['tipo'], $allowedRoles)) {
            JsonResponse::forbidden('No tienes permisos para realizar esta acción');
        }
        
        return $payload;
    }
    
    /**
     * Obtiene un usuario por ID
     */
    public function getUserById($id) {
        $stmt = $this->db->prepare("
            SELECT id, nombre, apellido, email, telefono, tipo, activo, fecha_registro
            FROM usuarios 
            WHERE id = ? AND activo = 1
        ");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Obtiene un usuario por email
     */
    public function getUserByEmail($email) {
        $stmt = $this->db->prepare("
            SELECT id, nombre, apellido, email, telefono, tipo, password_hash, activo, fecha_registro
            FROM usuarios 
            WHERE email = ? AND activo = 1
        ");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }
    
    /**
     * Verifica una contraseña
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Genera un hash de contraseña
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Registra un nuevo usuario
     */
    public function register($userData) {
        // Validar datos requeridos
        $requiredFields = ['nombre', 'apellido', 'email', 'password', 'tipo'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                JsonResponse::badRequest("El campo '$field' es requerido");
            }
        }
        
        // Validar email
        if (!validateEmail($userData['email'])) {
            JsonResponse::badRequest('Email no válido');
        }
        
        // Validar teléfono si se proporciona
        if (!empty($userData['telefono']) && !validateChileanPhone($userData['telefono'])) {
            JsonResponse::badRequest('Teléfono no válido. Formato: +56 9 XXXX XXXX');
        }
        
        // Verificar que el email no esté en uso
        if ($this->getUserByEmail($userData['email'])) {
            JsonResponse::badRequest('El email ya está registrado');
        }
        
        // Validar contraseña
        if (strlen($userData['password']) < 6) {
            JsonResponse::badRequest('La contraseña debe tener al menos 6 caracteres');
        }
        
        // Validar tipo de usuario
        $allowedTypes = ['usuario', 'dueno', 'admin'];
        if (!in_array($userData['tipo'], $allowedTypes)) {
            JsonResponse::badRequest('Tipo de usuario no válido');
        }
        
        try {
            $this->db->beginTransaction();
            
            // Insertar usuario
            $stmt = $this->db->prepare("
                INSERT INTO usuarios (nombre, apellido, email, telefono, password_hash, tipo, activo, fecha_registro)
                VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $stmt->execute([
                sanitizeInput($userData['nombre']),
                sanitizeInput($userData['apellido']),
                sanitizeInput($userData['email']),
                sanitizeInput($userData['telefono'] ?? null),
                $this->hashPassword($userData['password']),
                $userData['tipo']
            ]);
            
            $userId = $this->db->lastInsertId();
            
            $this->db->commit();
            
            // Obtener usuario creado
            $user = $this->getUserById($userId);
            
            // Generar token
            $token = $this->generateToken($user);
            
            return [
                'token' => $token,
                'usuario' => $user
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            logError('Error en registro de usuario: ' . $e->getMessage(), $userData);
            JsonResponse::error('Error al registrar usuario');
        }
    }
    
    /**
     * Inicia sesión de usuario
     */
    public function login($email, $password) {
        // Validar datos
        if (empty($email) || empty($password)) {
            JsonResponse::badRequest('Email y contraseña son requeridos');
        }
        
        // Obtener usuario
        $user = $this->getUserByEmail($email);
        
        if (!$user || !$this->verifyPassword($password, $user['password_hash'])) {
            JsonResponse::badRequest('Credenciales inválidas');
        }
        
        // Actualizar último acceso
        $stmt = $this->db->prepare("UPDATE usuarios SET ultimo_acceso = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Remover password_hash del resultado
        unset($user['password_hash']);
        
        // Generar token
        $token = $this->generateToken($user);
        
        return [
            'token' => $token,
            'usuario' => $user
        ];
    }
    
    /**
     * Actualiza información del usuario
     */
    public function updateUser($userId, $userData) {
        $allowedFields = ['nombre', 'apellido', 'telefono'];
        $updateFields = [];
        $values = [];
        
        foreach ($allowedFields as $field) {
            if (isset($userData[$field])) {
                $updateFields[] = "$field = ?";
                $values[] = sanitizeInput($userData[$field]);
            }
        }
        
        if (empty($updateFields)) {
            JsonResponse::badRequest('No hay campos para actualizar');
        }
        
        // Validar teléfono si se está actualizando
        if (isset($userData['telefono']) && !empty($userData['telefono']) && !validateChileanPhone($userData['telefono'])) {
            JsonResponse::badRequest('Teléfono no válido. Formato: +56 9 XXXX XXXX');
        }
        
        $values[] = $userId;
        
        $stmt = $this->db->prepare("
            UPDATE usuarios 
            SET " . implode(', ', $updateFields) . ", fecha_actualizacion = NOW()
            WHERE id = ? AND activo = 1
        ");
        
        if ($stmt->execute($values)) {
            return $this->getUserById($userId);
        }
        
        JsonResponse::error('Error al actualizar usuario');
    }
    
    /**
     * Cambia la contraseña del usuario
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        // Obtener usuario con contraseña
        $stmt = $this->db->prepare("SELECT password_hash FROM usuarios WHERE id = ? AND activo = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            JsonResponse::notFound('Usuario no encontrado');
        }
        
        // Verificar contraseña actual
        if (!$this->verifyPassword($currentPassword, $user['password_hash'])) {
            JsonResponse::badRequest('Contraseña actual incorrecta');
        }
        
        // Validar nueva contraseña
        if (strlen($newPassword) < 6) {
            JsonResponse::badRequest('La nueva contraseña debe tener al menos 6 caracteres');
        }
        
        // Actualizar contraseña
        $stmt = $this->db->prepare("
            UPDATE usuarios 
            SET password_hash = ?, fecha_actualizacion = NOW()
            WHERE id = ?
        ");
        
        if ($stmt->execute([$this->hashPassword($newPassword), $userId])) {
            return true;
        }
        
        JsonResponse::error('Error al cambiar contraseña');
    }
}
?>
