<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Función para cargar datos mock
function loadMockData($filename) {
    $filepath = __DIR__ . '/../assets/mock/' . $filename;
    if (!file_exists($filepath)) {
        return [];
    }
    $content = file_get_contents($filepath);
    return json_decode($content, true) ?: [];
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // Cargar datos de comunas
        $comunas = loadMockData('comunas_cl.json');
        
        // Filtro por búsqueda (opcional)
        $busqueda = $_GET['q'] ?? '';
        if (!empty($busqueda)) {
            $comunas = array_filter($comunas, function($comuna) use ($busqueda) {
                return stripos($comuna['nombre'], $busqueda) !== false ||
                       stripos($comuna['region'], $busqueda) !== false;
            });
        }
        
        // Filtro por región (opcional)
        $region = $_GET['region'] ?? '';
        if (!empty($region)) {
            $comunas = array_filter($comunas, function($comuna) use ($region) {
                return stripos($comuna['region'], $region) !== false;
            });
        }
        
        // Ordenar alfabéticamente por nombre
        usort($comunas, function($a, $b) {
            return strcmp($a['nombre'], $b['nombre']);
        });
        
        // Paginación (opcional)
        $pagina = intval($_GET['pagina'] ?? 1);
        $porPagina = intval($_GET['por_pagina'] ?? 50);
        $offset = ($pagina - 1) * $porPagina;
        
        $total = count($comunas);
        $comunasPaginadas = array_slice($comunas, $offset, $porPagina);
        
        // Obtener regiones únicas para filtros
        $todasComunas = loadMockData('comunas_cl.json');
        $regiones = array_unique(array_column($todasComunas, 'region'));
        sort($regiones);
        
        echo json_encode([
            'success' => true,
            'data' => $comunasPaginadas,
            'regiones' => $regiones,
            'pagination' => [
                'total' => $total,
                'pagina' => $pagina,
                'por_pagina' => $porPagina,
                'total_paginas' => ceil($total / $porPagina)
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Método no permitido'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
