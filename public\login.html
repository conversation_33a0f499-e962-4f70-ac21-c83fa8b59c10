<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Se<PERSON>ón - CanchaCL</title>
    <meta name="description" content="Inicia sesión en CanchaCL para gestionar tus reservas y acceder a funciones exclusivas.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="registro.html">Registrarse</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <section class="section">
            <div class="container">
                <div style="max-width: 400px; margin: 0 auto;">
                    <div class="card">
                        <div class="card-header text-center">
                            <h1>Iniciar Sesión</h1>
                            <p class="text-gris-600">Accede a tu cuenta de CanchaCL</p>
                        </div>
                        
                        <div class="card-body">
                            <!-- Alertas -->
                            <div id="login-alerts"></div>
                            
                            <!-- Usuarios de prueba -->
                            <div class="alert alert-info" style="margin-bottom: var(--espacio-lg);">
                                <strong>Usuarios de prueba:</strong><br>
                                <small>
                                    <strong>Usuario:</strong> <EMAIL> / 123456<br>
                                    <strong>Admin:</strong> <EMAIL> / admin123
                                </small>
                            </div>
                            
                            <form id="login-form">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email</label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        name="email" 
                                        class="form-input" 
                                        placeholder="<EMAIL>"
                                        required
                                        autocomplete="email"
                                    >
                                    <div class="form-error" id="email-error"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="password" class="form-label">Contraseña</label>
                                    <input 
                                        type="password" 
                                        id="password" 
                                        name="password" 
                                        class="form-input" 
                                        placeholder="Tu contraseña"
                                        required
                                        autocomplete="current-password"
                                    >
                                    <div class="form-error" id="password-error"></div>
                                </div>
                                
                                <div class="form-check">
                                    <input type="checkbox" id="remember" name="remember" class="form-check-input">
                                    <label for="remember" class="form-check-label">
                                        Recordar mi sesión
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-full btn-lg" id="login-btn">
                                    Iniciar Sesión
                                </button>
                            </form>
                            
                            <div class="text-center" style="margin-top: var(--espacio-lg);">
                                <a href="#" class="text-sm text-rojo">¿Olvidaste tu contraseña?</a>
                            </div>
                        </div>
                        
                        <div class="card-footer text-center">
                            <p class="text-sm text-gris-600">
                                ¿No tienes cuenta? 
                                <a href="registro.html" class="text-rojo font-medium">Regístrate aquí</a>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Beneficios de crear cuenta -->
                    <div class="card" style="margin-top: var(--espacio-xl);">
                        <div class="card-header text-center">
                            <h3>Beneficios de tener cuenta</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 gap-4">
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9 12L11 14L15 10M21 12C21 16.971 16.971 21 12 21S3 16.971 3 12 7.029 3 12 3 21 7.029 21 12Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Gestiona tus reservas</h4>
                                        <p class="text-sm text-gris-600">Ve el historial y estado de todas tus reservas</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Canchas favoritas</h4>
                                        <p class="text-sm text-gris-600">Guarda tus canchas preferidas para acceso rápido</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Reservas más rápidas</h4>
                                        <p class="text-sm text-gris-600">Datos guardados para reservar en segundos</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <div style="width: 24px; height: 24px; background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M13.73 21C13.5542 21.3031 13.3019 21.5547 12.9982 21.7295C12.6946 21.9044 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9044 11.0018 21.7295C10.6982 21.5547 10.4458 21.3031 10.27 21" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Notificaciones</h4>
                                        <p class="text-sm text-gris-600">Recibe recordatorios y ofertas especiales</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar si ya está logueado
            const token = localStorage.getItem('token');
            if (token) {
                // Redirigir al perfil si ya está logueado
                window.location.href = 'perfil.html';
                return;
            }
            
            // Configurar formulario de login
            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const alertsContainer = document.getElementById('login-alerts');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Limpiar errores anteriores
                clearErrors();
                clearAlerts();
                
                // Obtener datos del formulario
                const formData = new FormData(loginForm);
                const email = formData.get('email').trim();
                const password = formData.get('password');
                const remember = formData.get('remember');
                
                // Validaciones básicas
                let hasErrors = false;
                
                if (!email) {
                    showFieldError('email', 'El email es requerido');
                    hasErrors = true;
                } else if (!isValidEmail(email)) {
                    showFieldError('email', 'El email no tiene un formato válido');
                    hasErrors = true;
                }
                
                if (!password) {
                    showFieldError('password', 'La contraseña es requerida');
                    hasErrors = true;
                }
                
                if (hasErrors) return;
                
                // Mostrar estado de carga
                const originalText = loginBtn.textContent;
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<div class="loading" style="margin-right: var(--espacio-sm);"></div>Iniciando sesión...';
                
                try {
                    // Llamar a la API de login
                    if (window.API) {
                        const response = await window.API.login({ email, password });
                        
                        if (response.success) {
                            // Guardar datos de sesión
                            localStorage.setItem('token', response.data.token);
                            localStorage.setItem('usuario', JSON.stringify(response.data.usuario));
                            
                            if (remember) {
                                localStorage.setItem('remember_login', 'true');
                            }
                            
                            // Mostrar mensaje de éxito
                            showAlert('success', '¡Bienvenido! Redirigiendo...');
                            
                            // Redirigir después de un breve delay
                            setTimeout(() => {
                                const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'perfil.html';
                                window.location.href = redirectUrl;
                            }, 1500);
                            
                        } else {
                            showAlert('error', response.message || 'Error al iniciar sesión');
                        }
                    } else {
                        showAlert('error', 'Error de conexión. Inténtalo nuevamente.');
                    }
                    
                } catch (error) {
                    console.error('Error en login:', error);
                    showAlert('error', 'Error de conexión. Inténtalo nuevamente.');
                } finally {
                    // Restaurar botón
                    loginBtn.disabled = false;
                    loginBtn.textContent = originalText;
                }
            });
            
            // Funciones auxiliares
            function clearErrors() {
                document.querySelectorAll('.form-error').forEach(error => {
                    error.textContent = '';
                });
                document.querySelectorAll('.form-input').forEach(input => {
                    input.classList.remove('error');
                });
            }
            
            function showFieldError(fieldName, message) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const inputElement = document.getElementById(fieldName);
                
                if (errorElement) errorElement.textContent = message;
                if (inputElement) inputElement.classList.add('error');
            }
            
            function clearAlerts() {
                alertsContainer.innerHTML = '';
            }
            
            function showAlert(type, message) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
                alertsContainer.innerHTML = `
                    <div class="alert ${alertClass}">
                        ${message}
                    </div>
                `;
            }
            
            function isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }
        });
    </script>
</body>
</html>
