/* CanchaCL - Page-specific Styles */

/* Home Page */
.hero {
  background: linear-gradient(135deg, var(--rojo) 0%, var(--rojo-800) 100%);
  color: var(--blanco);
  padding: var(--espacio-3xl) 0;
  text-align: center;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--espacio-md);
  color: var(--blanco);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--espacio-2xl);
  opacity: 0.9;
}

.search-form {
  background-color: var(--blanco);
  border-radius: var(--radio);
  padding: var(--espacio-lg);
  box-shadow: var(--shadow-lg);
  max-width: 800px;
  margin: 0 auto;
}

.search-grid {
  display: grid;
  gap: var(--espacio-md);
  grid-template-columns: 1fr;
}

.search-button {
  grid-column: 1 / -1;
}

/* Resultados Page */
.filters-section {
  background-color: var(--gris-100);
  padding: var(--espacio-lg) 0;
  border-bottom: 1px solid var(--gris-200);
}

.filters-toggle {
  display: flex;
  align-items: center;
  gap: var(--espacio-sm);
  margin-bottom: var(--espacio-md);
}

.filters-content {
  display: none;
}

.filters-content.active {
  display: block;
}

.filters-grid {
  display: grid;
  gap: var(--espacio-md);
  grid-template-columns: 1fr;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--espacio-lg);
  flex-wrap: wrap;
  gap: var(--espacio-md);
}

.results-count {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--negro);
}

.sort-select {
  min-width: 200px;
}

.results-grid {
  display: grid;
  gap: var(--espacio-lg);
  grid-template-columns: 1fr;
}

.no-results {
  text-align: center;
  padding: var(--espacio-3xl) 0;
}

.no-results-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--espacio-lg);
  opacity: 0.5;
}

/* Cancha Detail Page */
.cancha-header {
  margin-bottom: var(--espacio-xl);
}

.cancha-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--espacio-sm);
}

.cancha-location {
  font-size: var(--font-size-lg);
  color: var(--gris-600);
  margin-bottom: var(--espacio-md);
}

.cancha-rating-section {
  display: flex;
  align-items: center;
  gap: var(--espacio-lg);
  margin-bottom: var(--espacio-lg);
}

.cancha-gallery {
  margin-bottom: var(--espacio-xl);
}

.gallery-main {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--radio);
  margin-bottom: var(--espacio-md);
}

.gallery-thumbs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--espacio-sm);
}

.gallery-thumb {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.gallery-thumb:hover,
.gallery-thumb.active {
  opacity: 1;
}

.cancha-info {
  display: grid;
  gap: var(--espacio-xl);
  grid-template-columns: 1fr;
}

.info-section {
  background-color: var(--blanco);
  border: 1px solid var(--gris-200);
  border-radius: var(--radio);
  padding: var(--espacio-lg);
}

.info-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--espacio-md);
  color: var(--negro);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--espacio-md);
}

.service-item {
  display: flex;
  align-items: center;
  gap: var(--espacio-sm);
  padding: var(--espacio-sm);
  background-color: var(--gris-100);
  border-radius: var(--border-radius-sm);
}

.service-icon {
  width: 20px;
  height: 20px;
  fill: var(--rojo);
}

/* Reserva Page */
.reserva-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--espacio-xl);
  padding: 0 var(--espacio-md);
}

.step {
  display: flex;
  align-items: center;
  gap: var(--espacio-sm);
  padding: var(--espacio-sm) var(--espacio-md);
  border-radius: var(--border-radius-full);
  background-color: var(--gris-100);
  color: var(--gris-600);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.step.active {
  background-color: var(--rojo);
  color: var(--blanco);
}

.step.completed {
  background-color: var(--rojo-200);
  color: var(--rojo-800);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--blanco);
  color: var(--gris-600);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.step.active .step-number {
  background-color: var(--blanco);
  color: var(--rojo);
}

.step.completed .step-number {
  background-color: var(--rojo);
  color: var(--blanco);
}

.reserva-content {
  max-width: 600px;
  margin: 0 auto;
}

.horarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: var(--espacio-sm);
  margin-bottom: var(--espacio-lg);
}

.horario-slot {
  padding: var(--espacio-sm);
  text-align: center;
  border: 1px solid var(--gris-300);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--blanco);
}

.horario-slot:hover {
  border-color: var(--rojo);
}

.horario-slot.selected {
  background-color: var(--rojo);
  color: var(--blanco);
  border-color: var(--rojo);
}

.horario-slot.disabled {
  background-color: var(--gris-100);
  color: var(--gris-400);
  cursor: not-allowed;
}

.resumen-reserva {
  background-color: var(--gris-100);
  border-radius: var(--radio);
  padding: var(--espacio-lg);
  margin-bottom: var(--espacio-lg);
}

.resumen-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--espacio-sm) 0;
  border-bottom: 1px solid var(--gris-200);
}

.resumen-item:last-child {
  border-bottom: none;
  font-weight: 600;
  font-size: var(--font-size-lg);
}

/* Admin Pages */
.admin-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: var(--espacio-xl);
  flex-wrap: wrap;
  gap: var(--espacio-md);
}

.admin-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--espacio-lg);
  margin-bottom: var(--espacio-xl);
}

.stat-card {
  background-color: var(--blanco);
  border: 1px solid var(--gris-200);
  border-radius: var(--radio);
  padding: var(--espacio-lg);
  text-align: center;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--rojo);
  margin-bottom: var(--espacio-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gris-600);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--blanco);
  border-radius: var(--radio);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th,
.data-table td {
  padding: var(--espacio-md);
  text-align: left;
  border-bottom: 1px solid var(--gris-200);
}

.data-table th {
  background-color: var(--gris-100);
  font-weight: 600;
  color: var(--negro);
}

.data-table tr:hover {
  background-color: var(--gris-50);
}

.table-actions {
  display: flex;
  gap: var(--espacio-sm);
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .search-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .search-button {
    grid-column: 1 / -1;
  }
  
  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .gallery-main {
    height: 400px;
  }
  
  .admin-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .search-grid {
    grid-template-columns: 2fr 2fr 1fr 1fr;
  }
  
  .search-button {
    grid-column: auto;
  }
  
  .filters-content {
    display: block;
  }
  
  .filters-toggle {
    display: none;
  }
  
  .filters-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .cancha-info {
    grid-template-columns: 2fr 1fr;
  }
  
  .admin-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .results-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .gallery-main {
    height: 500px;
  }
}
