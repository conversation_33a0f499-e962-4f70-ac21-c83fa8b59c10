/**
 * CanchaCL - Main Application
 * Funcionalidad principal de la aplicación
 */

class CanchaCLApp {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.init();
    }

    /**
     * Inicializa la aplicación
     */
    init() {
        this.checkAuthentication();
        this.setupGlobalEventListeners();
        this.setupNavigation();
        this.setupForms();
        this.initializePage();
    }

    /**
     * Verifica el estado de autenticación
     */
    checkAuthentication() {
        const token = localStorage.getItem('token');
        const userData = localStorage.getItem('usuario');

        if (token && userData) {
            try {
                this.currentUser = JSON.parse(userData);
                this.isAuthenticated = true;
                this.updateNavigationForAuthenticatedUser();
            } catch (error) {
                console.error('Error parsing user data:', error);
                this.logout();
            }
        }
    }

    /**
     * Actualiza la navegación para usuarios autenticados
     */
    updateNavigationForAuthenticatedUser() {
        // Ocultar enlaces de login/registro
        const loginLinks = document.querySelectorAll('#nav-login, #nav-login-mobile');
        const registerLinks = document.querySelectorAll('a[href="registro.html"]');
        
        loginLinks.forEach(link => {
            if (link) link.style.display = 'none';
        });
        
        registerLinks.forEach(link => {
            if (link && link.classList.contains('btn')) {
                link.style.display = 'none';
            }
        });

        // Mostrar enlaces de usuario autenticado
        const userLinks = document.querySelectorAll('a[href="perfil.html"], #logout-link');
        userLinks.forEach(link => {
            if (link) link.style.display = 'block';
        });

        // Mostrar enlace admin si corresponde
        if (this.currentUser.tipo === 'admin' || this.currentUser.tipo === 'dueno') {
            const adminLinks = document.querySelectorAll('a[href="admin/dashboard.html"], #admin-link');
            adminLinks.forEach(link => {
                if (link) link.style.display = 'block';
            });
        }

        // Actualizar nombre de usuario en móvil
        const userNameMobile = document.getElementById('user-name-mobile');
        if (userNameMobile) {
            userNameMobile.textContent = this.currentUser.nombre;
        }
    }

    /**
     * Configura event listeners globales
     */
    setupGlobalEventListeners() {
        // Manejar clicks en enlaces de login
        document.addEventListener('click', (e) => {
            if (e.target.matches('#nav-login, #nav-login-mobile')) {
                e.preventDefault();
                if (this.isAuthenticated) {
                    window.location.href = 'perfil.html';
                } else {
                    window.location.href = 'login.html';
                }
            }

            // Manejar logout
            if (e.target.matches('#logout-link, #logout-desktop, #logout-mobile')) {
                e.preventDefault();
                this.logout();
            }

            // Manejar enlaces de reserva
            if (e.target.matches('a[href^="reserva.html"]')) {
                if (!this.isAuthenticated) {
                    e.preventDefault();
                    const href = e.target.getAttribute('href');
                    window.location.href = `login.html?redirect=${encodeURIComponent(href)}`;
                }
            }
        });

        // Manejar formulario de búsqueda principal
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleMainSearch.bind(this));
        }

        // Manejar filtros en página de resultados
        const filtersForm = document.getElementById('filters-form');
        if (filtersForm) {
            filtersForm.addEventListener('submit', this.handleFiltersSubmit.bind(this));
        }

        // Manejar toggle de filtros
        const filtersToggle = document.getElementById('filters-toggle');
        if (filtersToggle) {
            filtersToggle.addEventListener('click', this.toggleFilters.bind(this));
        }
    }

    /**
     * Configura la navegación
     */
    setupNavigation() {
        // Marcar página activa en navegación
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('nav a[href]');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                link.classList.add('active');
            }
        });
    }

    /**
     * Configura formularios
     */
    setupForms() {
        // Agregar validación en tiempo real
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[type="email"]')) {
                this.validateEmail(e.target);
            }
            
            if (e.target.matches('input[type="tel"]')) {
                this.validatePhone(e.target);
            }
        });

        // Formatear teléfonos chilenos automáticamente
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[type="tel"]') && e.target.placeholder.includes('+56')) {
                this.formatChileanPhone(e.target);
            }
        });
    }

    /**
     * Inicializa funcionalidad específica de la página
     */
    initializePage() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        switch (currentPage) {
            case 'index.html':
            case '':
                this.initHomePage();
                break;
            case 'resultados.html':
                this.initResultsPage();
                break;
            case 'cancha.html':
                this.initCanchaPage();
                break;
            case 'reserva.html':
                this.initReservaPage();
                break;
        }
    }

    /**
     * Inicializa la página de inicio
     */
    initHomePage() {
        // Configurar fecha mínima en el formulario de búsqueda
        const fechaInput = document.getElementById('fecha');
        if (fechaInput) {
            const today = new Date().toISOString().split('T')[0];
            fechaInput.min = today;
            fechaInput.value = today;
        }

        // Cargar comunas
        this.loadComunas('comuna');
    }

    /**
     * Inicializa la página de resultados
     */
    initResultsPage() {
        // Cargar comunas para filtros
        this.loadComunas('filter-comuna');

        // Aplicar filtros desde URL
        this.applyFiltersFromURL();
    }

    /**
     * Inicializa la página de cancha
     */
    initCanchaPage() {
        // La funcionalidad específica se maneja en cancha.html
    }

    /**
     * Inicializa la página de reserva
     */
    initReservaPage() {
        // Verificar autenticación
        if (!this.isAuthenticated) {
            window.location.href = 'login.html?redirect=reserva.html';
            return;
        }
    }

    /**
     * Maneja el formulario de búsqueda principal
     */
    handleMainSearch(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const params = new URLSearchParams();
        
        // Agregar parámetros no vacíos
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                params.append(key, value);
            }
        }
        
        // Redirigir a resultados
        window.location.href = `resultados.html?${params.toString()}`;
    }

    /**
     * Maneja el envío de filtros
     */
    handleFiltersSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const params = new URLSearchParams(window.location.search);
        
        // Actualizar parámetros
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                params.set(key, value);
            } else {
                params.delete(key);
            }
        }
        
        // Actualizar URL sin recargar
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.pushState({}, '', newUrl);
        
        // Aplicar filtros
        if (typeof cargarCanchas === 'function') {
            const filtros = Object.fromEntries(params.entries());
            cargarCanchas(filtros);
        }
    }

    /**
     * Toggle de filtros en móvil
     */
    toggleFilters() {
        const filtersContent = document.getElementById('filters-content');
        if (filtersContent) {
            filtersContent.classList.toggle('active');
        }
    }

    /**
     * Aplica filtros desde la URL
     */
    applyFiltersFromURL() {
        const params = new URLSearchParams(window.location.search);
        
        params.forEach((value, key) => {
            const input = document.getElementById(`filter-${key}`);
            if (input) {
                input.value = value;
            }
        });
    }

    /**
     * Carga comunas en un select
     */
    async loadComunas(selectId) {
        const select = document.getElementById(selectId);
        if (!select || !window.API) return;

        try {
            const response = await window.API.getComunas();
            if (response.success) {
                response.data.forEach(comuna => {
                    const option = document.createElement('option');
                    option.value = comuna.nombre;
                    option.textContent = comuna.nombre;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error cargando comunas:', error);
        }
    }

    /**
     * Valida un email
     */
    validateEmail(input) {
        const email = input.value.trim();
        const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        
        if (email && !isValid) {
            input.classList.add('error');
            this.showFieldError(input, 'Email no válido');
        } else {
            input.classList.remove('error');
            this.clearFieldError(input);
        }
    }

    /**
     * Valida un teléfono
     */
    validatePhone(input) {
        const phone = input.value.trim();
        const isValid = /^\+56\s?[0-9]\s?[0-9]{4}\s?[0-9]{4}$/.test(phone);
        
        if (phone && !isValid) {
            input.classList.add('error');
            this.showFieldError(input, 'Formato: +56 9 XXXX XXXX');
        } else {
            input.classList.remove('error');
            this.clearFieldError(input);
        }
    }

    /**
     * Formatea teléfono chileno automáticamente
     */
    formatChileanPhone(input) {
        let value = input.value.replace(/\D/g, '');
        
        if (value.startsWith('56')) {
            value = value.substring(2);
        }
        
        if (value.length > 0) {
            if (value.length <= 1) {
                value = '+56 ' + value;
            } else if (value.length <= 5) {
                value = '+56 ' + value.substring(0, 1) + ' ' + value.substring(1);
            } else {
                value = '+56 ' + value.substring(0, 1) + ' ' + value.substring(1, 5) + ' ' + value.substring(5, 9);
            }
        }
        
        input.value = value;
    }

    /**
     * Muestra error en un campo
     */
    showFieldError(field, message) {
        let errorElement = field.parentNode.querySelector('.form-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'form-error';
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
    }

    /**
     * Limpia error de un campo
     */
    clearFieldError(field) {
        const errorElement = field.parentNode.querySelector('.form-error');
        if (errorElement) {
            errorElement.textContent = '';
        }
    }

    /**
     * Cierra sesión
     */
    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('usuario');
        localStorage.removeItem('remember_login');
        
        // Limpiar datos de reserva temporal
        ['reserva_cancha_id', 'reserva_fecha', 'reserva_hora', 'reserva_duracion'].forEach(key => {
            localStorage.removeItem(key);
        });
        
        this.currentUser = null;
        this.isAuthenticated = false;
        
        // Redirigir al inicio
        window.location.href = 'index.html';
    }

    /**
     * Obtiene el usuario actual
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Verifica si el usuario está autenticado
     */
    isUserAuthenticated() {
        return this.isAuthenticated;
    }
}

// Inicializar aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.CanchaCL = new CanchaCLApp();
});

// Funciones globales de utilidad
window.CanchaCLUtils = {
    /**
     * Formatea una fecha para mostrar
     */
    formatearFecha(fecha) {
        const date = new Date(fecha + 'T00:00:00');
        return date.toLocaleDateString('es-CL', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    /**
     * Formatea un precio
     */
    formatearPrecio(precio) {
        return '$' + precio.toLocaleString('es-CL');
    },

    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Scroll suave a elemento
     */
    scrollTo(selector, offset = 0) {
        const element = document.querySelector(selector);
        if (element) {
            const top = element.offsetTop - offset;
            window.scrollTo({
                top: top,
                behavior: 'smooth'
            });
        }
    }
};

// Manejar errores globales
window.addEventListener('error', (e) => {
    console.error('Error global:', e.error);
    
    // En producción, enviar errores a servicio de logging
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        // Aquí se enviaría el error a un servicio como Sentry
    }
});

// Manejar promesas rechazadas
window.addEventListener('unhandledrejection', (e) => {
    console.error('Promise rechazada:', e.reason);
    e.preventDefault();
});
