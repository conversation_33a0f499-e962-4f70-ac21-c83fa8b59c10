# CanchaCL - Plataforma de Arriendo de Canchas Deportivas

## Descripción
CanchaCL es una aplicación web para buscar, comparar y reservar canchas deportivas en Chile. Permite a los usuarios encontrar canchas de fútbol, tenis, p<PERSON><PERSON>, b<PERSON><PERSON> y baby fútbol por comuna, fecha y horario, mientras que los dueños pueden publicar sus canchas y gestionar reservas.

## Características Principales
- 🏟️ Búsqueda de canchas por deporte, comuna, fecha y horario
- 📱 Diseño mobile-first completamente responsive
- 💳 Sistema de reservas con pago simulado
- 👤 Panel de usuario para gestionar reservas y favoritos
- 🏢 Panel administrativo para dueños de canchas
- 🎨 Diseño moderno con colores corporativos (Rojo #D32F2F y Negro #111111)

## Tecnologías
- **Frontend**: HTML5, CSS3 (vanilla), JavaScript (ES6+)
- **Backend**: PHP (procedimental)
- **Datos**: JSON mock files (sin base de datos)
- **Sin dependencias externas**: No frameworks, no librerías

## Estructura del Proyecto
```
/public/                    # Archivos públicos
  index.html               # Página principal
  resultados.html          # Listado de canchas
  cancha.html              # Detalle de cancha
  reserva.html             # Flujo de reserva
  login.html               # Inicio de sesión
  registro.html            # Registro de usuario
  perfil.html              # Perfil de usuario
  admin/                   # Panel administrativo
    dashboard.html         # Dashboard principal
    canchas.html           # Gestión de canchas
    reservas.html          # Gestión de reservas

/assets/                    # Recursos estáticos
  /css/                    # Hojas de estilo
    base.css               # Reset, variables, tipografías
    layout.css             # Grid, header, footer
    components.css         # Botones, tarjetas, formularios
    pages.css              # Estilos específicos por página
  /js/                     # JavaScript
    app.js                 # Navegación y handlers UI
    api.js                 # Comunicación con API
    validators.js          # Validación de formularios
    ui.js                  # Renderizado dinámico
  /img/                    # Imágenes placeholder
  /mock/                   # Datos de prueba
    canchas.json           # Datos de canchas
    comunas_cl.json        # Comunas de Chile
    horarios.json          # Horarios disponibles

/api/                      # Endpoints PHP
  canchas.php              # API de canchas
  reservas.php             # API de reservas
  auth_login.php           # Autenticación
  auth_registro.php        # Registro
  comunas.php              # API de comunas

/templates/                # Templates PHP
  header.php               # Header reutilizable
  footer.php               # Footer reutilizable
```

## Instalación y Uso

### Requisitos
- PHP 7.4 o superior
- Navegador web moderno

### Instrucciones
1. Clona o descarga el proyecto
2. Abre una terminal en el directorio del proyecto
3. Ejecuta el servidor PHP integrado:
   ```bash
   php -S localhost:8000 -t public
   ```
4. Abre tu navegador en `http://localhost:8000`

### Usuarios de Prueba
- **Usuario regular**: <EMAIL> / 123456
- **Administrador**: <EMAIL> / admin123

## Funcionalidades

### Para Usuarios
- Búsqueda de canchas por múltiples filtros
- Visualización de detalles de cancha con galería
- Sistema de reservas paso a paso
- Gestión de perfil y reservas
- Sistema de favoritos

### Para Administradores
- CRUD completo de canchas
- Gestión de horarios y precios
- Visualización de reservas
- Dashboard con estadísticas básicas

## Datos Mock
El prototipo utiliza archivos JSON para simular una base de datos:
- **50+ comunas** de Chile
- **30+ canchas** de ejemplo con datos realistas
- **Horarios** de 08:00 a 23:00 cada hora
- **Precios** en CLP con formato chileno

## Diseño
- **Colores principales**: Rojo (#D32F2F), Negro (#111111)
- **Colores secundarios**: Gris claro (#F4F4F5), Gris medio (#9CA3AF), Blanco (#FFFFFF)
- **Tipografía**: Fuentes del sistema
- **Responsive**: Mobile-first con breakpoints para tablet y desktop
- **Accesibilidad**: Contraste adecuado, tamaños mínimos, roles ARIA

## Formato Chileno
- **Moneda**: CLP sin decimales (ej: $15.990)
- **Fechas**: dd-mm-aaaa
- **Horarios**: Formato 24h (ej: 18:00)
- **Idioma**: Español de Chile

## Desarrollo
Este es un prototipo funcional. Para producción se recomienda:
- Implementar base de datos real (MySQL/PostgreSQL)
- Agregar autenticación segura con JWT
- Implementar pasarela de pago real
- Agregar tests automatizados
- Optimizar para SEO
- Implementar cache y CDN

## Licencia
Prototipo educativo - CanchaCL © 2024
