<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Función para cargar datos mock
function loadMockData($filename) {
    $filepath = __DIR__ . '/../assets/mock/' . $filename;
    if (!file_exists($filepath)) {
        return [];
    }
    $content = file_get_contents($filepath);
    return json_decode($content, true) ?: [];
}

// Función para formatear precio chileno
function formatPrecioCLP($precio) {
    return '$' . number_format($precio, 0, ',', '.');
}

// Función para filtrar canchas
function filtrarCanchas($canchas, $filtros) {
    return array_filter($canchas, function($cancha) use ($filtros) {
        // Filtro por deporte
        if (!empty($filtros['deporte']) && $cancha['deporte'] !== $filtros['deporte']) {
            return false;
        }
        
        // Filtro por comuna
        if (!empty($filtros['comuna']) && stripos($cancha['comuna'], $filtros['comuna']) === false) {
            return false;
        }
        
        // Filtro por precio mínimo
        if (!empty($filtros['precio_min']) && $cancha['precioHoraCLP'] < intval($filtros['precio_min'])) {
            return false;
        }
        
        // Filtro por precio máximo
        if (!empty($filtros['precio_max']) && $cancha['precioHoraCLP'] > intval($filtros['precio_max'])) {
            return false;
        }
        
        // Filtro por superficie
        if (!empty($filtros['superficie']) && stripos($cancha['superficie'], $filtros['superficie']) === false) {
            return false;
        }
        
        // Filtro por disponibilidad
        if (!empty($filtros['disponibilidad']) && $cancha['disponibilidad'] !== $filtros['disponibilidad']) {
            return false;
        }
        
        return true;
    });
}

// Función para ordenar canchas
function ordenarCanchas($canchas, $orden) {
    switch ($orden) {
        case 'precio_asc':
            usort($canchas, function($a, $b) {
                return $a['precioHoraCLP'] - $b['precioHoraCLP'];
            });
            break;
        case 'precio_desc':
            usort($canchas, function($a, $b) {
                return $b['precioHoraCLP'] - $a['precioHoraCLP'];
            });
            break;
        case 'rating_desc':
            usort($canchas, function($a, $b) {
                return $b['rating'] <=> $a['rating'];
            });
            break;
        case 'nombre_asc':
            usort($canchas, function($a, $b) {
                return strcmp($a['nombre'], $b['nombre']);
            });
            break;
        default:
            // Por defecto ordenar por rating descendente
            usort($canchas, function($a, $b) {
                return $b['rating'] <=> $a['rating'];
            });
    }
    return $canchas;
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // Cargar datos de canchas
        $canchas = loadMockData('canchas.json');
        
        // Verificar si se solicita una cancha específica
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $cancha = array_filter($canchas, function($c) use ($id) {
                return $c['id'] === $id;
            });
            
            if (empty($cancha)) {
                http_response_code(404);
                echo json_encode([
                    'error' => true,
                    'message' => 'Cancha no encontrada'
                ]);
                exit();
            }
            
            $cancha = array_values($cancha)[0];
            
            // Formatear precio para mostrar
            $cancha['precioFormateado'] = formatPrecioCLP($cancha['precioHoraCLP']);
            
            // Simular horarios disponibles para hoy
            $horarios = loadMockData('horarios.json');
            $horariosDisponibles = array_map(function($h) use ($cancha) {
                // Simular disponibilidad basada en el estado de la cancha
                $disponible = true;
                if ($cancha['disponibilidad'] === 'agotado') {
                    $disponible = false;
                } elseif ($cancha['disponibilidad'] === 'pocas') {
                    // Solo algunos horarios disponibles
                    $disponible = rand(0, 1) === 1;
                }
                
                return [
                    'hora' => $h['hora'],
                    'disponible' => $disponible,
                    'precio' => $cancha['precioHoraCLP']
                ];
            }, $horarios);
            
            $cancha['horariosHoy'] = $horariosDisponibles;
            
            echo json_encode([
                'success' => true,
                'data' => $cancha
            ]);
            exit();
        }
        
        // Obtener filtros de la query string
        $filtros = [
            'deporte' => $_GET['deporte'] ?? '',
            'comuna' => $_GET['comuna'] ?? '',
            'precio_min' => $_GET['precio_min'] ?? '',
            'precio_max' => $_GET['precio_max'] ?? '',
            'superficie' => $_GET['superficie'] ?? '',
            'disponibilidad' => $_GET['disponibilidad'] ?? ''
        ];
        
        // Filtrar canchas
        $canchasFiltradas = filtrarCanchas($canchas, $filtros);
        
        // Ordenar canchas
        $orden = $_GET['orden'] ?? 'rating_desc';
        $canchasOrdenadas = ordenarCanchas($canchasFiltradas, $orden);
        
        // Paginación
        $pagina = intval($_GET['pagina'] ?? 1);
        $porPagina = intval($_GET['por_pagina'] ?? 12);
        $offset = ($pagina - 1) * $porPagina;
        
        $total = count($canchasOrdenadas);
        $canchasPaginadas = array_slice($canchasOrdenadas, $offset, $porPagina);
        
        // Formatear precios para mostrar
        $canchasPaginadas = array_map(function($cancha) {
            $cancha['precioFormateado'] = formatPrecioCLP($cancha['precioHoraCLP']);
            return $cancha;
        }, $canchasPaginadas);
        
        echo json_encode([
            'success' => true,
            'data' => $canchasPaginadas,
            'pagination' => [
                'total' => $total,
                'pagina' => $pagina,
                'por_pagina' => $porPagina,
                'total_paginas' => ceil($total / $porPagina)
            ],
            'filtros_aplicados' => array_filter($filtros)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Método no permitido'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
