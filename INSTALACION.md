# Guía de Instalación - CanchaCL

Esta guía te ayudará a instalar y configurar CanchaCL en tu entorno local o de producción.

## 📋 Requisitos del Sistema

### Requisitos Mínimos
- **PHP 7.4+** (Recomendado: PHP 8.0+)
- **MySQL 5.7+** o **MariaDB 10.3+**
- **Servidor Web**: Apache 2.4+ o Nginx 1.18+
- **Memoria RAM**: 512MB mínimo (1GB recomendado)
- **Espacio en disco**: 500MB mínimo

### Extensiones PHP Requeridas
- `pdo_mysql` - Para conexión a MySQL
- `json` - Para manejo de JSON
- `openssl` - Para JWT y encriptación
- `mbstring` - Para manejo de strings multibyte
- `fileinfo` - Para validación de archivos
- `gd` o `imagick` - Para procesamiento de imágenes (opcional)

## 🚀 Instalación Paso a Paso

### 1. Descargar el Proyecto

#### Opción A: Clonar desde Git
```bash
git clone https://github.com/tu-usuario/canchacl.git
cd canchacl
```

#### Opción B: Descargar ZIP
1. Descargar el archivo ZIP del proyecto
2. Extraer en la carpeta deseada
3. Navegar a la carpeta del proyecto

### 2. Configurar Base de Datos

#### Crear Base de Datos
```sql
-- Conectar a MySQL
mysql -u root -p

-- Crear base de datos
CREATE DATABASE canchacl CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario (opcional pero recomendado)
CREATE USER 'canchacl_user'@'localhost' IDENTIFIED BY 'tu_password_segura';
GRANT ALL PRIVILEGES ON canchacl.* TO 'canchacl_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Importar Esquema
```bash
# Importar estructura de la base de datos
mysql -u root -p canchacl < database/schema.sql

# Importar datos de prueba (opcional)
mysql -u root -p canchacl < database/seed.sql
```

### 3. Configurar PHP

#### Copiar Archivo de Configuración
```bash
# Copiar archivo de configuración de ejemplo
cp api/config.php.example api/config.php
```

#### Editar Configuración
Abrir `api/config.php` y configurar:

```php
<?php
// Configuración de base de datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'canchacl');
define('DB_USER', 'canchacl_user');
define('DB_PASS', 'tu_password_segura');

// Configuración JWT (cambiar por una clave segura)
define('JWT_SECRET', 'tu_clave_secreta_muy_segura_aqui_2024');

// Configuración de archivos
define('UPLOAD_PATH', '../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Entorno (development/production)
define('APP_ENV', 'development');
?>
```

### 4. Configurar Permisos

#### Linux/macOS
```bash
# Dar permisos de escritura
chmod 755 uploads/
chmod 755 assets/mock/
chmod 644 api/*.php

# Si usas Apache, asegurar permisos del usuario web
sudo chown -R www-data:www-data uploads/
```

#### Windows
1. Clic derecho en carpeta `uploads/`
2. Propiedades → Seguridad
3. Dar permisos de escritura al usuario del servidor web

### 5. Configurar Servidor Web

#### Opción A: Servidor PHP Integrado (Desarrollo)
```bash
# Navegar a la carpeta del proyecto
cd canchacl

# Iniciar servidor en puerto 8000
php -S localhost:8000 -t public/

# Abrir navegador en http://localhost:8000
```

#### Opción B: Apache

**Crear VirtualHost:**
```apache
<VirtualHost *:80>
    ServerName canchacl.local
    DocumentRoot /ruta/a/canchacl/public
    
    <Directory /ruta/a/canchacl/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/canchacl_error.log
    CustomLog ${APACHE_LOG_DIR}/canchacl_access.log combined
</VirtualHost>
```

**Crear .htaccess en public/:**
```apache
RewriteEngine On

# Redirigir a HTTPS (producción)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Manejar rutas de la aplicación
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.html [QSA,L]

# Headers de seguridad
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache para recursos estáticos
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>
```

#### Opción C: Nginx

**Configuración del sitio:**
```nginx
server {
    listen 80;
    server_name canchacl.local;
    root /ruta/a/canchacl/public;
    index index.html;

    # Logs
    access_log /var/log/nginx/canchacl_access.log;
    error_log /var/log/nginx/canchacl_error.log;

    # Manejar archivos estáticos
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # API PHP
    location /api/ {
        try_files $uri $uri/ @php;
    }

    location @php {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Manejar rutas de la aplicación
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Headers de seguridad
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
```

## 🔧 Configuración Adicional

### Variables de Entorno

Para mayor seguridad, puedes usar variables de entorno:

```bash
# Crear archivo .env
echo "DB_HOST=localhost" > .env
echo "DB_NAME=canchacl" >> .env
echo "DB_USER=canchacl_user" >> .env
echo "DB_PASS=tu_password" >> .env
echo "JWT_SECRET=tu_clave_secreta" >> .env
```

### Configurar HTTPS (Producción)

#### Con Let's Encrypt (Certbot)
```bash
# Instalar certbot
sudo apt install certbot python3-certbot-apache

# Obtener certificado
sudo certbot --apache -d tu-dominio.com

# Renovación automática
sudo crontab -e
# Agregar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Configurar Backups

#### Script de Backup MySQL
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/ruta/a/backups"
DB_NAME="canchacl"
DB_USER="canchacl_user"
DB_PASS="tu_password"

# Crear backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/canchacl_$DATE.sql

# Comprimir
gzip $BACKUP_DIR/canchacl_$DATE.sql

# Eliminar backups antiguos (más de 30 días)
find $BACKUP_DIR -name "canchacl_*.sql.gz" -mtime +30 -delete
```

## 🧪 Verificar Instalación

### 1. Verificar Conexión a Base de Datos
```bash
# Probar conexión
php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=canchacl', 'canchacl_user', 'tu_password');
    echo 'Conexión exitosa\n';
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . '\n';
}
"
```

### 2. Verificar API
```bash
# Probar endpoint de comunas
curl http://localhost:8000/api/comunas.php

# Debería retornar JSON con lista de comunas
```

### 3. Verificar Frontend
1. Abrir navegador en `http://localhost:8000`
2. Verificar que la página carga correctamente
3. Probar búsqueda de canchas
4. Probar registro/login

### 4. Usuarios de Prueba

El sistema incluye usuarios de prueba:
- **Usuario**: `<EMAIL>` / `123456`
- **Admin**: `<EMAIL>` / `admin123`
- **Dueño**: `<EMAIL>` / `dueno123`

## 🐛 Solución de Problemas

### Error: "No se puede conectar a la base de datos"
- Verificar credenciales en `config.php`
- Verificar que MySQL esté ejecutándose
- Verificar permisos del usuario de BD

### Error: "Token JWT inválido"
- Verificar que `JWT_SECRET` esté configurado
- Verificar extensión OpenSSL de PHP

### Error: "No se pueden subir archivos"
- Verificar permisos de carpeta `uploads/`
- Verificar configuración `upload_max_filesize` en PHP

### Error 404 en rutas
- Verificar configuración de rewrite en servidor web
- Verificar que mod_rewrite esté habilitado (Apache)

### Problemas de CORS
- Verificar configuración de CORS en `config.php`
- Agregar dominio a `ALLOWED_ORIGINS`

## 📞 Soporte

Si tienes problemas con la instalación:

1. **Revisar logs**:
   - PHP: `/var/log/php_errors.log`
   - Apache: `/var/log/apache2/error.log`
   - Nginx: `/var/log/nginx/error.log`

2. **Contactar soporte**:
   - Email: <EMAIL>
   - GitHub Issues: [Reportar problema](https://github.com/tu-usuario/canchacl/issues)

3. **Documentación adicional**:
   - [README.md](README.md) - Información general
   - [API.md](docs/API.md) - Documentación de API
   - [DEPLOYMENT.md](docs/DEPLOYMENT.md) - Guía de despliegue

---

¡Listo! Tu instalación de CanchaCL debería estar funcionando correctamente. 🎉
