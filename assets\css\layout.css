/* CanchaCL - Layout Styles */

/* Contenedores principales */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--espacio-md);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

/* Grid system */
.grid {
  display: grid;
  gap: var(--espacio-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-8 { grid-column: span 8 / span 8; }
.col-span-12 { grid-column: span 12 / span 12; }

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

.gap-0 { gap: 0; }
.gap-1 { gap: var(--espacio-xs); }
.gap-2 { gap: var(--espacio-sm); }
.gap-4 { gap: var(--espacio-md); }
.gap-6 { gap: var(--espacio-lg); }
.gap-8 { gap: var(--espacio-xl); }

/* Header */
.header {
  background-color: var(--blanco);
  border-bottom: 1px solid var(--gris-200);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 var(--espacio-md);
}

.logo {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--negro);
  text-decoration: none;
}

.logo .accent {
  color: var(--rojo);
}

.nav-desktop {
  display: none;
}

.nav-mobile {
  display: flex;
  align-items: center;
  gap: var(--espacio-md);
}

.menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--espacio-sm);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.menu-toggle:hover {
  background-color: var(--gris-100);
}

.menu-toggle svg {
  width: 24px;
  height: 24px;
  fill: var(--negro);
}

/* Navigation menu */
.nav-menu {
  position: fixed;
  top: 65px;
  left: 0;
  right: 0;
  background-color: var(--blanco);
  border-bottom: 1px solid var(--gris-200);
  box-shadow: var(--shadow-md);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  z-index: var(--z-dropdown);
}

.nav-menu.active {
  transform: translateY(0);
}

.nav-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-menu li {
  border-bottom: 1px solid var(--gris-100);
  margin: 0;
}

.nav-menu a {
  display: block;
  padding: var(--espacio-md);
  color: var(--negro);
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.nav-menu a:hover {
  background-color: var(--gris-100);
  text-decoration: none;
}

/* Main content */
.main {
  min-height: calc(100vh - 64px - 120px); /* header height - footer height */
  padding: var(--espacio-lg) 0;
}

.section {
  margin-bottom: var(--espacio-3xl);
}

.section-header {
  margin-bottom: var(--espacio-xl);
  text-align: center;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--negro);
  margin-bottom: var(--espacio-sm);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gris-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Footer */
.footer {
  background-color: var(--negro);
  color: var(--blanco);
  padding: var(--espacio-xl) 0 var(--espacio-lg);
  margin-top: auto;
}

.footer-content {
  display: grid;
  gap: var(--espacio-xl);
  margin-bottom: var(--espacio-lg);
}

.footer-section h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--espacio-md);
  color: var(--blanco);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--espacio-sm);
}

.footer-section a {
  color: var(--gris-400);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: var(--blanco);
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid var(--gris-700);
  padding-top: var(--espacio-lg);
  text-align: center;
  color: var(--gris-400);
  font-size: var(--font-size-sm);
}

/* Responsive design */
@media (min-width: 640px) {
  .container {
    padding: 0 var(--espacio-lg);
  }
  
  .grid-cols-sm-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-sm-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .header-content {
    padding: 0 var(--espacio-lg);
  }
  
  .nav-desktop {
    display: flex;
    align-items: center;
    gap: var(--espacio-lg);
  }
  
  .nav-mobile {
    display: none;
  }
  
  .nav-menu {
    position: static;
    transform: none;
    background: none;
    border: none;
    box-shadow: none;
    display: flex;
    align-items: center;
  }
  
  .nav-menu ul {
    display: flex;
    align-items: center;
    gap: var(--espacio-lg);
  }
  
  .nav-menu li {
    border: none;
  }
  
  .nav-menu a {
    padding: var(--espacio-sm) 0;
  }
  
  .nav-menu a:hover {
    background: none;
    color: var(--rojo);
  }
  
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-cols-md-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-md-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-md-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--espacio-xl);
  }
  
  .main {
    padding: var(--espacio-3xl) 0;
  }
  
  .footer-content {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-cols-lg-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-lg-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-lg-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-cols-lg-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}
