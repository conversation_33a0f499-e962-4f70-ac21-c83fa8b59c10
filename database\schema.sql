-- CanchaCL - Esquema de Base de Datos
-- Base de datos para plataforma de reserva de canchas deportivas

-- Crear base de datos
CREATE DATABASE IF NOT EXISTS canchacl CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE canchacl;

-- Tabla de usuarios
CREATE TABLE usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    apellido VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    telefono VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    tipo ENUM('usuario', 'dueno', 'admin') NOT NULL DEFAULT 'usuario',
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ultimo_acceso TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_tipo (tipo),
    INDEX idx_activo (activo)
);

-- Tabla de regiones
CREATE TABLE regiones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    codigo VARCHAR(10) NOT NULL UNIQUE
);

-- Tabla de comunas
CREATE TABLE comunas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    region_id INT NOT NULL,
    
    FOREIGN KEY (region_id) REFERENCES regiones(id),
    INDEX idx_region (region_id)
);

-- Tabla de canchas
CREATE TABLE canchas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(200) NOT NULL,
    descripcion TEXT,
    direccion VARCHAR(300) NOT NULL,
    comuna_id INT NOT NULL,
    deporte ENUM('futbol', 'tenis', 'padel', 'basquet', 'baby_futbol', 'voleibol') NOT NULL,
    superficie VARCHAR(50),
    precio_hora_clp INT NOT NULL,
    capacidad_jugadores INT,
    largo_metros DECIMAL(5,2),
    ancho_metros DECIMAL(5,2),
    techada BOOLEAN DEFAULT FALSE,
    iluminacion BOOLEAN DEFAULT FALSE,
    estacionamiento BOOLEAN DEFAULT FALSE,
    camarines BOOLEAN DEFAULT FALSE,
    cafeteria BOOLEAN DEFAULT FALSE,
    seguridad BOOLEAN DEFAULT FALSE,
    dueno_id INT NOT NULL,
    activa BOOLEAN NOT NULL DEFAULT TRUE,
    verificada BOOLEAN NOT NULL DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (comuna_id) REFERENCES comunas(id),
    FOREIGN KEY (dueno_id) REFERENCES usuarios(id),
    INDEX idx_deporte (deporte),
    INDEX idx_comuna (comuna_id),
    INDEX idx_dueno (dueno_id),
    INDEX idx_activa (activa),
    INDEX idx_precio (precio_hora_clp),
    INDEX idx_rating (rating)
);

-- Tabla de fotos de canchas
CREATE TABLE cancha_fotos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cancha_id INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    descripcion VARCHAR(200),
    orden INT DEFAULT 0,
    principal BOOLEAN DEFAULT FALSE,
    fecha_subida TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (cancha_id) REFERENCES canchas(id) ON DELETE CASCADE,
    INDEX idx_cancha (cancha_id),
    INDEX idx_principal (principal)
);

-- Tabla de horarios de canchas
CREATE TABLE cancha_horarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cancha_id INT NOT NULL,
    dia_semana TINYINT NOT NULL, -- 0=Domingo, 1=Lunes, ..., 6=Sábado
    hora_inicio TIME NOT NULL,
    hora_fin TIME NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (cancha_id) REFERENCES canchas(id) ON DELETE CASCADE,
    INDEX idx_cancha (cancha_id),
    INDEX idx_dia (dia_semana),
    UNIQUE KEY unique_cancha_dia_hora (cancha_id, dia_semana, hora_inicio)
);

-- Tabla de reservas
CREATE TABLE reservas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cancha_id INT NOT NULL,
    usuario_id INT NOT NULL,
    fecha DATE NOT NULL,
    hora_inicio TIME NOT NULL,
    hora_fin TIME NOT NULL,
    duracion_horas INT NOT NULL,
    precio_total_clp INT NOT NULL,
    estado ENUM('pendiente', 'confirmada', 'cancelada', 'completada') NOT NULL DEFAULT 'pendiente',
    metodo_pago ENUM('tarjeta', 'transferencia', 'efectivo') NOT NULL,
    notas TEXT,
    fecha_reserva TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (cancha_id) REFERENCES canchas(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    INDEX idx_cancha (cancha_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_fecha (fecha),
    INDEX idx_estado (estado),
    UNIQUE KEY unique_cancha_fecha_hora (cancha_id, fecha, hora_inicio)
);

-- Tabla de pagos
CREATE TABLE pagos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reserva_id INT NOT NULL,
    monto_clp INT NOT NULL,
    metodo ENUM('tarjeta', 'transferencia', 'efectivo') NOT NULL,
    estado ENUM('pendiente', 'procesando', 'completado', 'fallido', 'reembolsado') NOT NULL DEFAULT 'pendiente',
    referencia_externa VARCHAR(200), -- ID de transacción del procesador de pagos
    fecha_pago TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (reserva_id) REFERENCES reservas(id),
    INDEX idx_reserva (reserva_id),
    INDEX idx_estado (estado),
    INDEX idx_fecha (fecha_pago)
);

-- Tabla de reviews/calificaciones
CREATE TABLE reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cancha_id INT NOT NULL,
    usuario_id INT NOT NULL,
    reserva_id INT,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comentario TEXT,
    fecha_review TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (cancha_id) REFERENCES canchas(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (reserva_id) REFERENCES reservas(id),
    INDEX idx_cancha (cancha_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_rating (rating),
    UNIQUE KEY unique_usuario_cancha (usuario_id, cancha_id)
);

-- Tabla de favoritos
CREATE TABLE favoritos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    cancha_id INT NOT NULL,
    fecha_agregado TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (cancha_id) REFERENCES canchas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_usuario_cancha (usuario_id, cancha_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_cancha (cancha_id)
);

-- Tabla de notificaciones
CREATE TABLE notificaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    tipo ENUM('reserva_confirmada', 'reserva_cancelada', 'recordatorio', 'promocion', 'sistema') NOT NULL,
    titulo VARCHAR(200) NOT NULL,
    mensaje TEXT NOT NULL,
    leida BOOLEAN DEFAULT FALSE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_usuario (usuario_id),
    INDEX idx_leida (leida),
    INDEX idx_tipo (tipo)
);

-- Tabla de configuración del sistema
CREATE TABLE configuracion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    clave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT,
    descripcion VARCHAR(300),
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar datos iniciales

-- Regiones de Chile
INSERT INTO regiones (nombre, codigo) VALUES
('Arica y Parinacota', 'XV'),
('Tarapacá', 'I'),
('Antofagasta', 'II'),
('Atacama', 'III'),
('Coquimbo', 'IV'),
('Valparaíso', 'V'),
('Metropolitana', 'RM'),
('O''Higgins', 'VI'),
('Maule', 'VII'),
('Ñuble', 'XVI'),
('Biobío', 'VIII'),
('Araucanía', 'IX'),
('Los Ríos', 'XIV'),
('Los Lagos', 'X'),
('Aysén', 'XI'),
('Magallanes', 'XII');

-- Comunas de la Región Metropolitana (principales)
INSERT INTO comunas (nombre, region_id) VALUES
('Santiago', 7),
('Las Condes', 7),
('Providencia', 7),
('Ñuñoa', 7),
('La Reina', 7),
('Vitacura', 7),
('Lo Barnechea', 7),
('Maipú', 7),
('Puente Alto', 7),
('La Florida', 7),
('Peñalolén', 7),
('San Miguel', 7),
('Independencia', 7),
('Recoleta', 7),
('Quilicura', 7),
('Huechuraba', 7),
('Conchalí', 7),
('Renca', 7),
('Cerro Navia', 7),
('Quinta Normal', 7),
('Estación Central', 7),
('Pedro Aguirre Cerda', 7),
('San Joaquín', 7),
('Macul', 7),
('San Ramón', 7),
('La Cisterna', 7),
('El Bosque', 7),
('La Granja', 7),
('San Bernardo', 7),
('Calera de Tango', 7);

-- Usuario administrador inicial
INSERT INTO usuarios (nombre, apellido, email, password_hash, tipo) VALUES
('Admin', 'Sistema', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Configuración inicial del sistema
INSERT INTO configuracion (clave, valor, descripcion) VALUES
('sitio_nombre', 'CanchaCL', 'Nombre del sitio web'),
('sitio_descripcion', 'Plataforma para reservar canchas deportivas en Chile', 'Descripción del sitio'),
('email_contacto', '<EMAIL>', 'Email de contacto'),
('telefono_contacto', '+56 2 2234 5678', 'Teléfono de contacto'),
('comision_porcentaje', '5', 'Porcentaje de comisión por reserva'),
('reserva_anticipacion_dias', '30', 'Días máximos de anticipación para reservas'),
('reserva_cancelacion_horas', '24', 'Horas mínimas para cancelar una reserva'),
('mantenimiento_activo', 'false', 'Indica si el sitio está en mantenimiento');

-- Triggers para actualizar rating de canchas
DELIMITER //

CREATE TRIGGER actualizar_rating_cancha_insert
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
    UPDATE canchas 
    SET rating = (
        SELECT AVG(rating) 
        FROM reviews 
        WHERE cancha_id = NEW.cancha_id
    ),
    total_reviews = (
        SELECT COUNT(*) 
        FROM reviews 
        WHERE cancha_id = NEW.cancha_id
    )
    WHERE id = NEW.cancha_id;
END//

CREATE TRIGGER actualizar_rating_cancha_update
AFTER UPDATE ON reviews
FOR EACH ROW
BEGIN
    UPDATE canchas 
    SET rating = (
        SELECT AVG(rating) 
        FROM reviews 
        WHERE cancha_id = NEW.cancha_id
    ),
    total_reviews = (
        SELECT COUNT(*) 
        FROM reviews 
        WHERE cancha_id = NEW.cancha_id
    )
    WHERE id = NEW.cancha_id;
END//

CREATE TRIGGER actualizar_rating_cancha_delete
AFTER DELETE ON reviews
FOR EACH ROW
BEGIN
    UPDATE canchas 
    SET rating = COALESCE((
        SELECT AVG(rating) 
        FROM reviews 
        WHERE cancha_id = OLD.cancha_id
    ), 0),
    total_reviews = (
        SELECT COUNT(*) 
        FROM reviews 
        WHERE cancha_id = OLD.cancha_id
    )
    WHERE id = OLD.cancha_id;
END//

DELIMITER ;

-- Índices adicionales para optimización
CREATE INDEX idx_reservas_fecha_estado ON reservas(fecha, estado);
CREATE INDEX idx_canchas_deporte_comuna ON canchas(deporte, comuna_id);
CREATE INDEX idx_canchas_precio_rating ON canchas(precio_hora_clp, rating);

-- Vistas útiles
CREATE VIEW vista_canchas_completa AS
SELECT 
    c.*,
    co.nombre as comuna_nombre,
    r.nombre as region_nombre,
    u.nombre as dueno_nombre,
    u.email as dueno_email,
    u.telefono as dueno_telefono,
    COUNT(DISTINCT cf.id) as total_fotos,
    COUNT(DISTINCT rev.id) as total_reviews_count
FROM canchas c
LEFT JOIN comunas co ON c.comuna_id = co.id
LEFT JOIN regiones r ON co.region_id = r.id
LEFT JOIN usuarios u ON c.dueno_id = u.id
LEFT JOIN cancha_fotos cf ON c.id = cf.cancha_id
LEFT JOIN reviews rev ON c.id = rev.cancha_id
WHERE c.activa = TRUE
GROUP BY c.id;

CREATE VIEW vista_reservas_completa AS
SELECT 
    res.*,
    c.nombre as cancha_nombre,
    c.direccion as cancha_direccion,
    co.nombre as comuna_nombre,
    u.nombre as usuario_nombre,
    u.email as usuario_email,
    u.telefono as usuario_telefono,
    p.estado as pago_estado
FROM reservas res
LEFT JOIN canchas c ON res.cancha_id = c.id
LEFT JOIN comunas co ON c.comuna_id = co.id
LEFT JOIN usuarios u ON res.usuario_id = u.id
LEFT JOIN pagos p ON res.id = p.reserva_id;
