/* CanchaCL - Base Styles */

/* Variables CSS */
:root {
  --rojo: #D32F2F;
  --negro: #111111;
  --gris-100: #F4F4F5;
  --gris-400: #9CA3AF;
  --blanco: #FFFFFF;
  --radio: 12px;
  --espacio: 16px;
  
  /* Colores adicionales */
  --gris-200: #E5E7EB;
  --gris-300: #D1D5DB;
  --gris-500: #6B7280;
  --gris-600: #4B5563;
  --gris-700: #374151;
  --gris-800: #1F2937;
  --gris-900: #111827;
  
  /* Rojo variaciones */
  --rojo-50: #FFEBEE;
  --rojo-100: #FFCDD2;
  --rojo-200: #EF9A9A;
  --rojo-300: #E57373;
  --rojo-400: #EF5350;
  --rojo-500: #F44336;
  --rojo-600: #E53935;
  --rojo-700: #D32F2F;
  --rojo-800: #C62828;
  --rojo-900: #B71C1C;
  
  /* Espaciado */
  --espacio-xs: 4px;
  --espacio-sm: 8px;
  --espacio-md: 16px;
  --espacio-lg: 24px;
  --espacio-xl: 32px;
  --espacio-2xl: 48px;
  --espacio-3xl: 64px;
  
  /* Tipografía */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  /* Línea de altura */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* Sombras */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Bordes */
  --border-radius-sm: 6px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-full: 9999px;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Reset básico */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--leading-normal);
  color: var(--negro);
  background-color: var(--blanco);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Tipografía */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: var(--leading-tight);
  margin-bottom: var(--espacio-sm);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--espacio-md);
}

a {
  color: var(--rojo);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--rojo-800);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--rojo);
  outline-offset: 2px;
}

/* Listas */
ul, ol {
  margin-bottom: var(--espacio-md);
  padding-left: var(--espacio-lg);
}

li {
  margin-bottom: var(--espacio-xs);
}

/* Imágenes */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Elementos de formulario base */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Utilidades de texto */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-negro { color: var(--negro); }
.text-rojo { color: var(--rojo); }
.text-gris-400 { color: var(--gris-400); }
.text-gris-500 { color: var(--gris-500); }
.text-gris-600 { color: var(--gris-600); }
.text-blanco { color: var(--blanco); }

/* Utilidades de espaciado */
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

/* Utilidades de display */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

/* Utilidades de visibilidad */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive breakpoints */
@media (min-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
}

@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
}

@media (min-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
}
