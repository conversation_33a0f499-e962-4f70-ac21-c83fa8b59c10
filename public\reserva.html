<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservar Cancha - CanchaCL</title>
    <meta name="description" content="Completa tu reserva de cancha deportiva. Proceso seguro y confirmación inmediata.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="#" id="nav-login">Iniciar Sesión</a></li>
                            <li><a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="#" id="nav-login-mobile" class="btn btn-ghost btn-sm">Entrar</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="perfil.html">Mi Perfil</a></li>
                <li><a href="admin/dashboard.html">Panel Admin</a></li>
                <li><a href="#" id="logout-link">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Steps Indicator -->
        <section class="section" style="padding-bottom: 0;">
            <div class="container">
                <div class="reserva-steps">
                    <div class="step active" id="step-1">
                        <div class="step-number">1</div>
                        <span>Horario</span>
                    </div>
                    <div class="step" id="step-2">
                        <div class="step-number">2</div>
                        <span>Datos</span>
                    </div>
                    <div class="step" id="step-3">
                        <div class="step-number">3</div>
                        <span>Pago</span>
                    </div>
                    <div class="step" id="step-4">
                        <div class="step-number">4</div>
                        <span>Confirmación</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reserva Content -->
        <section class="section">
            <div class="container">
                <div class="reserva-content">
                    <!-- Step 1: Horario -->
                    <div id="step-content-1" class="step-content">
                        <div class="card">
                            <div class="card-header">
                                <h2>Selecciona horario</h2>
                                <p class="text-gris-600">Elige la fecha y hora para tu reserva</p>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="reserva-fecha" class="form-label">Fecha</label>
                                    <input type="date" id="reserva-fecha" name="fecha" class="form-input" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Horarios disponibles</label>
                                    <div class="horarios-grid" id="horarios-step1">
                                        <!-- Los horarios se cargan dinámicamente -->
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reserva-duracion" class="form-label">Duración</label>
                                    <select id="reserva-duracion" name="duracion" class="form-input form-select" required>
                                        <option value="1">1 hora</option>
                                        <option value="2">2 horas</option>
                                        <option value="3">3 horas</option>
                                        <option value="4">4 horas</option>
                                    </select>
                                </div>
                                
                                <button type="button" class="btn btn-primary btn-full" id="btn-step1" disabled>
                                    Continuar
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Datos -->
                    <div id="step-content-2" class="step-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h2>Datos de contacto</h2>
                                <p class="text-gris-600">Completa tus datos para la reserva</p>
                            </div>
                            <div class="card-body">
                                <form id="datos-form">
                                    <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                                        <div class="form-group">
                                            <label for="nombre" class="form-label">Nombre *</label>
                                            <input type="text" id="nombre" name="nombre" class="form-input" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="apellido" class="form-label">Apellido *</label>
                                            <input type="text" id="apellido" name="apellido" class="form-input" required>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="telefono" class="form-label">Teléfono *</label>
                                        <input type="tel" id="telefono" name="telefono" class="form-input" placeholder="+56 9 XXXX XXXX" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="notas" class="form-label">Notas adicionales</label>
                                        <textarea id="notas" name="notas" class="form-input form-textarea" placeholder="Información adicional sobre tu reserva (opcional)"></textarea>
                                    </div>
                                </form>
                                
                                <div class="flex gap-2">
                                    <button type="button" class="btn btn-outline flex-1" id="btn-back-step2">
                                        Volver
                                    </button>
                                    <button type="button" class="btn btn-primary flex-1" id="btn-step2">
                                        Continuar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Pago -->
                    <div id="step-content-3" class="step-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h2>Método de pago</h2>
                                <p class="text-gris-600">Selecciona cómo quieres pagar tu reserva</p>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="form-label">Método de pago</label>
                                    <div class="grid grid-cols-1 gap-3">
                                        <div class="form-check">
                                            <input type="radio" id="pago-tarjeta" name="metodoPago" value="tarjeta" class="form-check-input" checked>
                                            <label for="pago-tarjeta" class="form-check-label">
                                                <strong>Tarjeta de crédito/débito</strong><br>
                                                <span class="text-sm text-gris-600">Pago seguro con tarjeta</span>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check">
                                            <input type="radio" id="pago-transferencia" name="metodoPago" value="transferencia" class="form-check-input">
                                            <label for="pago-transferencia" class="form-check-label">
                                                <strong>Transferencia bancaria</strong><br>
                                                <span class="text-sm text-gris-600">Transferencia directa a cuenta</span>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check">
                                            <input type="radio" id="pago-efectivo" name="metodoPago" value="efectivo" class="form-check-input">
                                            <label for="pago-efectivo" class="form-check-label">
                                                <strong>Efectivo en el lugar</strong><br>
                                                <span class="text-sm text-gris-600">Paga directamente en la cancha</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Simulación de formulario de tarjeta -->
                                <div id="tarjeta-form" class="form-group">
                                    <div class="alert alert-info">
                                        <strong>Modo demo:</strong> Esta es una simulación. No se procesará ningún pago real.
                                    </div>
                                    
                                    <div class="grid grid-cols-1 gap-4">
                                        <div class="form-group">
                                            <label for="numero-tarjeta" class="form-label">Número de tarjeta</label>
                                            <input type="text" id="numero-tarjeta" class="form-input" placeholder="1234 5678 9012 3456" maxlength="19">
                                        </div>
                                        
                                        <div class="grid grid-cols-2 gap-4">
                                            <div class="form-group">
                                                <label for="expiry" class="form-label">Vencimiento</label>
                                                <input type="text" id="expiry" class="form-input" placeholder="MM/AA" maxlength="5">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="cvv" class="form-label">CVV</label>
                                                <input type="text" id="cvv" class="form-input" placeholder="123" maxlength="3">
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="titular" class="form-label">Nombre del titular</label>
                                            <input type="text" id="titular" class="form-input" placeholder="Nombre como aparece en la tarjeta">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex gap-2">
                                    <button type="button" class="btn btn-outline flex-1" id="btn-back-step3">
                                        Volver
                                    </button>
                                    <button type="button" class="btn btn-primary flex-1" id="btn-step3">
                                        Procesar pago
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Confirmación -->
                    <div id="step-content-4" class="step-content" style="display: none;">
                        <div class="card">
                            <div class="card-body text-center">
                                <div style="width: 80px; height: 80px; margin: 0 auto var(--espacio-lg); background-color: #DCFCE7; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 12L11 14L15 10M21 12C21 16.971 16.971 21 12 21S3 16.971 3 12 7.029 3 12 3 21 7.029 21 12Z" stroke="#166534" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                
                                <h2>¡Reserva confirmada! 🎉</h2>
                                <p class="text-gris-600" style="margin-bottom: var(--espacio-xl);">
                                    Tu reserva ha sido procesada exitosamente. Recibirás un email de confirmación en breve.
                                </p>
                                
                                <div class="resumen-reserva" style="text-align: left; margin-bottom: var(--espacio-xl);">
                                    <h4>Detalles de tu reserva</h4>
                                    <div class="resumen-item">
                                        <span>Número de reserva:</span>
                                        <span id="numero-reserva">#12345</span>
                                    </div>
                                    <div class="resumen-item">
                                        <span>Cancha:</span>
                                        <span id="confirmacion-cancha">-</span>
                                    </div>
                                    <div class="resumen-item">
                                        <span>Fecha:</span>
                                        <span id="confirmacion-fecha">-</span>
                                    </div>
                                    <div class="resumen-item">
                                        <span>Hora:</span>
                                        <span id="confirmacion-hora">-</span>
                                    </div>
                                    <div class="resumen-item">
                                        <span>Duración:</span>
                                        <span id="confirmacion-duracion">-</span>
                                    </div>
                                    <div class="resumen-item">
                                        <span>Total pagado:</span>
                                        <span id="confirmacion-total">-</span>
                                    </div>
                                </div>
                                
                                <div class="flex gap-2">
                                    <a href="perfil.html" class="btn btn-outline flex-1">Ver mis reservas</a>
                                    <a href="index.html" class="btn btn-primary flex-1">Buscar más canchas</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resumen lateral (visible en todos los pasos excepto confirmación) -->
                    <div class="card" id="resumen-lateral" style="margin-top: var(--espacio-xl);">
                        <div class="card-header">
                            <h3>Resumen de reserva</h3>
                        </div>
                        <div class="card-body">
                            <div id="cancha-info-resumen">
                                <!-- Se llena dinámicamente -->
                            </div>
                            
                            <div class="resumen-reserva" id="resumen-detalle" style="display: none;">
                                <div class="resumen-item">
                                    <span>Fecha:</span>
                                    <span id="resumen-fecha-lateral">-</span>
                                </div>
                                <div class="resumen-item">
                                    <span>Hora:</span>
                                    <span id="resumen-hora-lateral">-</span>
                                </div>
                                <div class="resumen-item">
                                    <span>Duración:</span>
                                    <span id="resumen-duracion-lateral">-</span>
                                </div>
                                <div class="resumen-item">
                                    <span>Total:</span>
                                    <span id="resumen-total-lateral">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        // Variables globales para el proceso de reserva
        let datosReserva = {
            canchaId: null,
            cancha: null,
            fecha: null,
            hora: null,
            duracion: 1,
            metodoPago: 'tarjeta',
            datosPersonales: {},
            total: 0
        };
        
        let pasoActual = 1;

        // Inicializar página de reserva
        document.addEventListener('DOMContentLoaded', function() {
            // Obtener datos de la URL o localStorage
            const urlParams = new URLSearchParams(window.location.search);
            datosReserva.canchaId = urlParams.get('cancha') || localStorage.getItem('reserva_cancha_id');
            datosReserva.fecha = urlParams.get('fecha') || localStorage.getItem('reserva_fecha');
            datosReserva.hora = urlParams.get('hora') || localStorage.getItem('reserva_hora');
            datosReserva.duracion = parseInt(urlParams.get('duracion') || localStorage.getItem('reserva_duracion') || '1');
            
            if (!datosReserva.canchaId) {
                // Redirigir si no hay datos de reserva
                window.location.href = 'resultados.html';
                return;
            }
            
            cargarDatosCancha();
            inicializarFormularios();
            configurarEventListeners();
            
            // Establecer fecha mínima como hoy
            const fechaInput = document.getElementById('reserva-fecha');
            const hoy = new Date().toISOString().split('T')[0];
            fechaInput.min = hoy;
            
            // Pre-llenar datos si existen
            if (datosReserva.fecha) {
                fechaInput.value = datosReserva.fecha;
            }
            if (datosReserva.duracion) {
                document.getElementById('reserva-duracion').value = datosReserva.duracion;
            }
        });

        // Función para cargar datos de la cancha
        function cargarDatosCancha() {
            if (window.API && datosReserva.canchaId) {
                window.API.getCancha(datosReserva.canchaId).then(response => {
                    if (response.success && response.data) {
                        datosReserva.cancha = response.data;
                        mostrarResumenCancha();
                        cargarHorariosDisponibles();
                    }
                }).catch(console.error);
            }
        }

        // Función para mostrar resumen de cancha
        function mostrarResumenCancha() {
            if (!datosReserva.cancha) return;
            
            const resumenContainer = document.getElementById('cancha-info-resumen');
            resumenContainer.innerHTML = `
                <div style="margin-bottom: var(--espacio-md);">
                    <h4>${datosReserva.cancha.nombre}</h4>
                    <p class="text-sm text-gris-600">${datosReserva.cancha.direccion}</p>
                    <p class="text-lg font-semibold text-rojo">${datosReserva.cancha.precioFormateado}/hora</p>
                </div>
            `;
        }

        // Función para cargar horarios disponibles
        function cargarHorariosDisponibles() {
            if (!datosReserva.cancha || !datosReserva.cancha.horariosHoy) return;
            
            const horariosContainer = document.getElementById('horarios-step1');
            horariosContainer.innerHTML = '';
            
            datosReserva.cancha.horariosHoy.forEach(horario => {
                const slot = document.createElement('button');
                slot.type = 'button';
                slot.className = `horario-slot ${!horario.disponible ? 'disabled' : ''}`;
                slot.textContent = horario.hora;
                slot.disabled = !horario.disponible;
                
                // Pre-seleccionar si hay hora guardada
                if (horario.hora === datosReserva.hora && horario.disponible) {
                    slot.classList.add('selected');
                    actualizarResumenLateral();
                    document.getElementById('btn-step1').disabled = false;
                }
                
                if (horario.disponible) {
                    slot.onclick = () => seleccionarHorario(horario.hora, slot);
                }
                
                horariosContainer.appendChild(slot);
            });
        }

        // Función para seleccionar horario
        function seleccionarHorario(hora, elemento) {
            // Remover selección anterior
            document.querySelectorAll('.horario-slot').forEach(slot => {
                slot.classList.remove('selected');
            });
            
            // Seleccionar nueva hora
            elemento.classList.add('selected');
            datosReserva.hora = hora;
            
            actualizarResumenLateral();
            document.getElementById('btn-step1').disabled = false;
        }

        // Función para actualizar resumen lateral
        function actualizarResumenLateral() {
            const fecha = document.getElementById('reserva-fecha').value || datosReserva.fecha;
            const duracion = parseInt(document.getElementById('reserva-duracion').value || datosReserva.duracion);
            
            if (fecha && datosReserva.hora && duracion && datosReserva.cancha) {
                datosReserva.fecha = fecha;
                datosReserva.duracion = duracion;
                datosReserva.total = datosReserva.cancha.precioHoraCLP * duracion;
                
                document.getElementById('resumen-fecha-lateral').textContent = formatearFecha(fecha);
                document.getElementById('resumen-hora-lateral').textContent = datosReserva.hora;
                document.getElementById('resumen-duracion-lateral').textContent = `${duracion} hora${duracion > 1 ? 's' : ''}`;
                document.getElementById('resumen-total-lateral').textContent = formatearPrecio(datosReserva.total);
                
                document.getElementById('resumen-detalle').style.display = 'block';
            }
        }

        // Función para configurar event listeners
        function configurarEventListeners() {
            // Step 1
            document.getElementById('reserva-fecha').addEventListener('change', () => {
                datosReserva.fecha = document.getElementById('reserva-fecha').value;
                cargarHorariosDisponibles();
                actualizarResumenLateral();
            });
            
            document.getElementById('reserva-duracion').addEventListener('change', actualizarResumenLateral);
            document.getElementById('btn-step1').addEventListener('click', () => irAPaso(2));
            
            // Step 2
            document.getElementById('btn-back-step2').addEventListener('click', () => irAPaso(1));
            document.getElementById('btn-step2').addEventListener('click', validarYContinuarPaso2);
            
            // Step 3
            document.getElementById('btn-back-step3').addEventListener('click', () => irAPaso(2));
            document.getElementById('btn-step3').addEventListener('click', procesarPago);
            
            // Método de pago
            document.querySelectorAll('input[name="metodoPago"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    datosReserva.metodoPago = e.target.value;
                    const tarjetaForm = document.getElementById('tarjeta-form');
                    tarjetaForm.style.display = e.target.value === 'tarjeta' ? 'block' : 'none';
                });
            });
        }

        // Función para ir a un paso específico
        function irAPaso(paso) {
            // Ocultar paso actual
            document.getElementById(`step-content-${pasoActual}`).style.display = 'none';
            document.getElementById(`step-${pasoActual}`).classList.remove('active');
            
            // Marcar pasos anteriores como completados
            for (let i = 1; i < paso; i++) {
                document.getElementById(`step-${i}`).classList.add('completed');
            }
            
            // Mostrar nuevo paso
            document.getElementById(`step-content-${paso}`).style.display = 'block';
            document.getElementById(`step-${paso}`).classList.add('active');
            
            // Ocultar resumen lateral en confirmación
            const resumenLateral = document.getElementById('resumen-lateral');
            resumenLateral.style.display = paso === 4 ? 'none' : 'block';
            
            pasoActual = paso;
            
            // Scroll al top
            window.scrollTo(0, 0);
        }

        // Función para validar y continuar paso 2
        function validarYContinuarPaso2() {
            const form = document.getElementById('datos-form');
            const formData = new FormData(form);
            
            // Validaciones básicas
            const nombre = formData.get('nombre').trim();
            const apellido = formData.get('apellido').trim();
            const email = formData.get('email').trim();
            const telefono = formData.get('telefono').trim();
            
            if (!nombre || !apellido || !email || !telefono) {
                alert('Por favor completa todos los campos obligatorios.');
                return;
            }
            
            if (!email.includes('@')) {
                alert('Por favor ingresa un email válido.');
                return;
            }
            
            // Guardar datos
            datosReserva.datosPersonales = {
                nombre,
                apellido,
                email,
                telefono,
                notas: formData.get('notas').trim()
            };
            
            irAPaso(3);
        }

        // Función para procesar pago (simulado)
        function procesarPago() {
            // Simular procesamiento
            const btnPago = document.getElementById('btn-step3');
            const textoOriginal = btnPago.textContent;
            
            btnPago.disabled = true;
            btnPago.innerHTML = '<div class="loading" style="margin-right: var(--espacio-sm);"></div>Procesando...';
            
            setTimeout(() => {
                // Simular creación de reserva
                if (window.API) {
                    const reservaData = {
                        canchaId: datosReserva.canchaId,
                        fecha: datosReserva.fecha,
                        hora: datosReserva.hora,
                        duracion: datosReserva.duracion,
                        metodoPago: datosReserva.metodoPago,
                        notas: datosReserva.datosPersonales.notas
                    };
                    
                    // En un entorno real, aquí se enviaría la reserva al servidor
                    console.log('Datos de reserva:', reservaData);
                    
                    // Mostrar confirmación
                    mostrarConfirmacion();
                    irAPaso(4);
                } else {
                    btnPago.disabled = false;
                    btnPago.textContent = textoOriginal;
                    alert('Error al procesar el pago. Inténtalo nuevamente.');
                }
            }, 2000);
        }

        // Función para mostrar confirmación
        function mostrarConfirmacion() {
            const numeroReserva = '#' + Math.floor(Math.random() * 90000 + 10000);
            
            document.getElementById('numero-reserva').textContent = numeroReserva;
            document.getElementById('confirmacion-cancha').textContent = datosReserva.cancha.nombre;
            document.getElementById('confirmacion-fecha').textContent = formatearFecha(datosReserva.fecha);
            document.getElementById('confirmacion-hora').textContent = datosReserva.hora;
            document.getElementById('confirmacion-duracion').textContent = `${datosReserva.duracion} hora${datosReserva.duracion > 1 ? 's' : ''}`;
            document.getElementById('confirmacion-total').textContent = formatearPrecio(datosReserva.total);
            
            // Limpiar localStorage
            ['reserva_cancha_id', 'reserva_fecha', 'reserva_hora', 'reserva_duracion'].forEach(key => {
                localStorage.removeItem(key);
            });
        }

        // Funciones auxiliares
        function formatearFecha(fecha) {
            const date = new Date(fecha + 'T00:00:00');
            return date.toLocaleDateString('es-CL', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function formatearPrecio(precio) {
            return '$' + precio.toLocaleString('es-CL');
        }

        function inicializarFormularios() {
            // Pre-llenar datos del usuario si está logueado
            const usuario = JSON.parse(localStorage.getItem('usuario') || '{}');
            if (usuario.nombre) {
                document.getElementById('nombre').value = usuario.nombre;
                document.getElementById('apellido').value = usuario.apellido || '';
                document.getElementById('email').value = usuario.email || '';
                document.getElementById('telefono').value = usuario.telefono || '';
            }
        }
    </script>
</body>
</html>
