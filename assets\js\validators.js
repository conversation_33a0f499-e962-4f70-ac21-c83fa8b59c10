/**
 * CanchaCL - Validators
 * Funciones de validación para formularios
 */

class Validators {
    /**
     * Valida un email
     * @param {string} email - Email a validar
     * @returns {Object} Resultado de validación
     */
    static email(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = regex.test(email);
        
        return {
            isValid,
            message: isValid ? '' : 'El email no tiene un formato válido'
        };
    }

    /**
     * Valida una contraseña
     * @param {string} password - Contraseña a validar
     * @param {Object} options - Opciones de validación
     * @returns {Object} Resultado de validación
     */
    static password(password, options = {}) {
        const {
            minLength = 6,
            requireUppercase = false,
            requireLowercase = false,
            requireNumbers = false,
            requireSpecialChars = false
        } = options;

        const errors = [];

        if (password.length < minLength) {
            errors.push(`Debe tener al menos ${minLength} caracteres`);
        }

        if (requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Debe contener al menos una mayúscula');
        }

        if (requireLowercase && !/[a-z]/.test(password)) {
            errors.push('Debe contener al menos una minúscula');
        }

        if (requireNumbers && !/\d/.test(password)) {
            errors.push('Debe contener al menos un número');
        }

        if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Debe contener al menos un carácter especial');
        }

        return {
            isValid: errors.length === 0,
            message: errors.join(', ')
        };
    }

    /**
     * Valida que dos contraseñas coincidan
     * @param {string} password - Contraseña original
     * @param {string} confirmPassword - Confirmación de contraseña
     * @returns {Object} Resultado de validación
     */
    static passwordMatch(password, confirmPassword) {
        const isValid = password === confirmPassword;
        
        return {
            isValid,
            message: isValid ? '' : 'Las contraseñas no coinciden'
        };
    }

    /**
     * Valida un teléfono chileno
     * @param {string} phone - Teléfono a validar
     * @returns {Object} Resultado de validación
     */
    static chileanPhone(phone) {
        const regex = /^\+56\s?[0-9]\s?[0-9]{4}\s?[0-9]{4}$/;
        const isValid = regex.test(phone);
        
        return {
            isValid,
            message: isValid ? '' : 'Formato válido: +56 9 XXXX XXXX'
        };
    }

    /**
     * Valida un RUT chileno
     * @param {string} rut - RUT a validar
     * @returns {Object} Resultado de validación
     */
    static chileanRUT(rut) {
        // Limpiar RUT
        const cleanRUT = rut.replace(/[^0-9kK]/g, '');
        
        if (cleanRUT.length < 8 || cleanRUT.length > 9) {
            return {
                isValid: false,
                message: 'RUT debe tener entre 8 y 9 caracteres'
            };
        }

        const body = cleanRUT.slice(0, -1);
        const dv = cleanRUT.slice(-1).toLowerCase();

        // Calcular dígito verificador
        let sum = 0;
        let multiplier = 2;

        for (let i = body.length - 1; i >= 0; i--) {
            sum += parseInt(body[i]) * multiplier;
            multiplier = multiplier === 7 ? 2 : multiplier + 1;
        }

        const remainder = sum % 11;
        const calculatedDV = remainder === 0 ? '0' : remainder === 1 ? 'k' : (11 - remainder).toString();

        const isValid = dv === calculatedDV;

        return {
            isValid,
            message: isValid ? '' : 'RUT no válido'
        };
    }

    /**
     * Valida un nombre (solo letras y espacios)
     * @param {string} name - Nombre a validar
     * @param {number} minLength - Longitud mínima
     * @returns {Object} Resultado de validación
     */
    static name(name, minLength = 2) {
        const regex = /^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/;
        const isValid = regex.test(name) && name.trim().length >= minLength;
        
        return {
            isValid,
            message: isValid ? '' : `Debe contener solo letras y tener al menos ${minLength} caracteres`
        };
    }

    /**
     * Valida un número
     * @param {string|number} value - Valor a validar
     * @param {Object} options - Opciones de validación
     * @returns {Object} Resultado de validación
     */
    static number(value, options = {}) {
        const {
            min = null,
            max = null,
            integer = false
        } = options;

        const num = parseFloat(value);
        
        if (isNaN(num)) {
            return {
                isValid: false,
                message: 'Debe ser un número válido'
            };
        }

        if (integer && !Number.isInteger(num)) {
            return {
                isValid: false,
                message: 'Debe ser un número entero'
            };
        }

        if (min !== null && num < min) {
            return {
                isValid: false,
                message: `Debe ser mayor o igual a ${min}`
            };
        }

        if (max !== null && num > max) {
            return {
                isValid: false,
                message: `Debe ser menor o igual a ${max}`
            };
        }

        return {
            isValid: true,
            message: ''
        };
    }

    /**
     * Valida una fecha
     * @param {string} date - Fecha a validar (YYYY-MM-DD)
     * @param {Object} options - Opciones de validación
     * @returns {Object} Resultado de validación
     */
    static date(date, options = {}) {
        const {
            minDate = null,
            maxDate = null,
            futureOnly = false,
            pastOnly = false
        } = options;

        const dateObj = new Date(date + 'T00:00:00');
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (isNaN(dateObj.getTime())) {
            return {
                isValid: false,
                message: 'Fecha no válida'
            };
        }

        if (futureOnly && dateObj < today) {
            return {
                isValid: false,
                message: 'La fecha debe ser futura'
            };
        }

        if (pastOnly && dateObj > today) {
            return {
                isValid: false,
                message: 'La fecha debe ser pasada'
            };
        }

        if (minDate && dateObj < new Date(minDate + 'T00:00:00')) {
            return {
                isValid: false,
                message: `La fecha debe ser posterior a ${minDate}`
            };
        }

        if (maxDate && dateObj > new Date(maxDate + 'T00:00:00')) {
            return {
                isValid: false,
                message: `La fecha debe ser anterior a ${maxDate}`
            };
        }

        return {
            isValid: true,
            message: ''
        };
    }

    /**
     * Valida una hora
     * @param {string} time - Hora a validar (HH:MM)
     * @returns {Object} Resultado de validación
     */
    static time(time) {
        const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        const isValid = regex.test(time);
        
        return {
            isValid,
            message: isValid ? '' : 'Formato de hora inválido (HH:MM)'
        };
    }

    /**
     * Valida que un campo no esté vacío
     * @param {string} value - Valor a validar
     * @param {string} fieldName - Nombre del campo
     * @returns {Object} Resultado de validación
     */
    static required(value, fieldName = 'Este campo') {
        const isValid = value && value.toString().trim().length > 0;
        
        return {
            isValid,
            message: isValid ? '' : `${fieldName} es requerido`
        };
    }

    /**
     * Valida la longitud de un texto
     * @param {string} value - Valor a validar
     * @param {number} min - Longitud mínima
     * @param {number} max - Longitud máxima
     * @returns {Object} Resultado de validación
     */
    static length(value, min = 0, max = Infinity) {
        const length = value ? value.toString().length : 0;
        
        if (length < min) {
            return {
                isValid: false,
                message: `Debe tener al menos ${min} caracteres`
            };
        }

        if (length > max) {
            return {
                isValid: false,
                message: `No puede tener más de ${max} caracteres`
            };
        }

        return {
            isValid: true,
            message: ''
        };
    }

    /**
     * Valida una URL
     * @param {string} url - URL a validar
     * @returns {Object} Resultado de validación
     */
    static url(url) {
        try {
            new URL(url);
            return {
                isValid: true,
                message: ''
            };
        } catch {
            return {
                isValid: false,
                message: 'URL no válida'
            };
        }
    }

    /**
     * Valida un archivo
     * @param {File} file - Archivo a validar
     * @param {Object} options - Opciones de validación
     * @returns {Object} Resultado de validación
     */
    static file(file, options = {}) {
        const {
            maxSize = 5 * 1024 * 1024, // 5MB por defecto
            allowedTypes = [],
            allowedExtensions = []
        } = options;

        if (!file) {
            return {
                isValid: false,
                message: 'No se ha seleccionado ningún archivo'
            };
        }

        if (file.size > maxSize) {
            const maxSizeMB = Math.round(maxSize / (1024 * 1024));
            return {
                isValid: false,
                message: `El archivo no puede ser mayor a ${maxSizeMB}MB`
            };
        }

        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            return {
                isValid: false,
                message: `Tipo de archivo no permitido. Tipos válidos: ${allowedTypes.join(', ')}`
            };
        }

        if (allowedExtensions.length > 0) {
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedExtensions.includes(extension)) {
                return {
                    isValid: false,
                    message: `Extensión no permitida. Extensiones válidas: ${allowedExtensions.join(', ')}`
                };
            }
        }

        return {
            isValid: true,
            message: ''
        };
    }

    /**
     * Valida múltiples campos de un formulario
     * @param {Object} data - Datos a validar
     * @param {Object} rules - Reglas de validación
     * @returns {Object} Resultado de validación
     */
    static validateForm(data, rules) {
        const errors = {};
        let isValid = true;

        Object.keys(rules).forEach(field => {
            const value = data[field];
            const fieldRules = rules[field];

            fieldRules.forEach(rule => {
                if (typeof rule === 'function') {
                    const result = rule(value);
                    if (!result.isValid) {
                        errors[field] = result.message;
                        isValid = false;
                    }
                } else if (typeof rule === 'object') {
                    const { validator, options = {}, message } = rule;
                    const result = validator(value, options);
                    if (!result.isValid) {
                        errors[field] = message || result.message;
                        isValid = false;
                    }
                }
            });
        });

        return {
            isValid,
            errors
        };
    }
}

// Funciones de utilidad para formateo
class Formatters {
    /**
     * Formatea un RUT chileno
     * @param {string} rut - RUT a formatear
     * @returns {string} RUT formateado
     */
    static formatRUT(rut) {
        const clean = rut.replace(/[^0-9kK]/g, '');
        if (clean.length < 2) return clean;
        
        const body = clean.slice(0, -1);
        const dv = clean.slice(-1);
        
        return body.replace(/\B(?=(\d{3})+(?!\d))/g, '.') + '-' + dv;
    }

    /**
     * Formatea un teléfono chileno
     * @param {string} phone - Teléfono a formatear
     * @returns {string} Teléfono formateado
     */
    static formatChileanPhone(phone) {
        let clean = phone.replace(/\D/g, '');
        
        if (clean.startsWith('56')) {
            clean = clean.substring(2);
        }
        
        if (clean.length === 9) {
            return `+56 ${clean.substring(0, 1)} ${clean.substring(1, 5)} ${clean.substring(5)}`;
        }
        
        return phone;
    }

    /**
     * Formatea un precio en pesos chilenos
     * @param {number} amount - Cantidad a formatear
     * @returns {string} Precio formateado
     */
    static formatCurrency(amount) {
        return '$' + amount.toLocaleString('es-CL');
    }

    /**
     * Formatea una fecha
     * @param {string|Date} date - Fecha a formatear
     * @param {string} format - Formato deseado
     * @returns {string} Fecha formateada
     */
    static formatDate(date, format = 'long') {
        const dateObj = typeof date === 'string' ? new Date(date + 'T00:00:00') : date;
        
        const options = {
            short: { day: 'numeric', month: 'short', year: 'numeric' },
            long: { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' },
            numeric: { day: '2-digit', month: '2-digit', year: 'numeric' }
        };
        
        return dateObj.toLocaleDateString('es-CL', options[format] || options.long);
    }
}

// Exportar para uso global
window.Validators = Validators;
window.Formatters = Formatters;

// Exportar para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Validators, Formatters };
}
