<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Administrativo - CanchaCL</title>
    <meta name="description" content="Panel de administración para gestionar canchas y reservas en CanchaCL.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../../assets/css/base.css">
    <link rel="stylesheet" href="../../assets/css/layout.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    Cancha<span class="accent">CL</span> <span class="text-sm text-gris-600">Admin</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="dashboard.html">Dashboard</a></li>
                            <li><a href="canchas.html">Canchas</a></li>
                            <li><a href="reservas.html">Reservas</a></li>
                            <li><a href="../perfil.html">Mi Perfil</a></li>
                            <li><a href="#" id="logout-desktop">Cerrar Sesión</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <span class="text-sm" id="admin-name-mobile">Admin</span>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="canchas.html">Canchas</a></li>
                <li><a href="reservas.html">Reservas</a></li>
                <li><a href="../perfil.html">Mi Perfil</a></li>
                <li><a href="../index.html">Ver sitio público</a></li>
                <li><a href="#" id="logout-mobile">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Admin Header -->
        <section class="section" style="background-color: var(--gris-100); padding-top: var(--espacio-lg); padding-bottom: var(--espacio-lg);">
            <div class="container">
                <div class="admin-header">
                    <div>
                        <h1>Panel Administrativo</h1>
                        <p class="text-gris-600">Gestiona tus canchas y reservas</p>
                    </div>
                    <div class="flex gap-2">
                        <a href="canchas.html" class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Nueva Cancha
                        </a>
                        <a href="../index.html" class="btn btn-outline">Ver sitio público</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="section">
            <div class="container">
                <div class="admin-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="total-canchas">-</div>
                        <div class="stat-label">Canchas Activas</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="reservas-mes">-</div>
                        <div class="stat-label">Reservas este mes</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="ingresos-mes">-</div>
                        <div class="stat-label">Ingresos del mes</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="ocupacion-promedio">-</div>
                        <div class="stat-label">Ocupación promedio</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Dashboard Content -->
        <section class="section" style="padding-top: 0;">
            <div class="container">
                <div class="grid grid-cols-1 grid-cols-lg-2 gap-8">
                    <!-- Reservas Recientes -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Reservas Recientes</h3>
                            <a href="reservas.html" class="text-sm text-rojo">Ver todas</a>
                        </div>
                        <div class="card-body">
                            <div id="reservas-recientes-loading" class="text-center" style="padding: var(--espacio-xl) 0;">
                                <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                                <p>Cargando reservas...</p>
                            </div>
                            
                            <div id="reservas-recientes-list" style="display: none;">
                                <!-- Se llenan dinámicamente -->
                            </div>
                            
                            <div id="no-reservas-recientes" class="text-center" style="padding: var(--espacio-xl) 0; display: none;">
                                <p class="text-gris-600">No hay reservas recientes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Canchas Populares -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Canchas Más Populares</h3>
                            <a href="canchas.html" class="text-sm text-rojo">Gestionar</a>
                        </div>
                        <div class="card-body">
                            <div id="canchas-populares-loading" class="text-center" style="padding: var(--espacio-xl) 0;">
                                <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
                                <p>Cargando estadísticas...</p>
                            </div>
                            
                            <div id="canchas-populares-list" style="display: none;">
                                <!-- Se llenan dinámicamente -->
                            </div>
                            
                            <div id="no-canchas-populares" class="text-center" style="padding: var(--espacio-xl) 0; display: none;">
                                <p class="text-gris-600">No hay datos disponibles</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Acciones Rápidas -->
                <div class="card" style="margin-top: var(--espacio-xl);">
                    <div class="card-header">
                        <h3>Acciones Rápidas</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-1 grid-cols-sm-2 grid-cols-lg-4 gap-4">
                            <a href="canchas.html" class="card text-center" style="text-decoration: none; color: inherit;">
                                <div class="card-body">
                                    <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 5V19M5 12H19" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <h4>Agregar Cancha</h4>
                                    <p class="text-sm text-gris-600">Registra una nueva cancha</p>
                                </div>
                            </a>
                            
                            <a href="reservas.html" class="card text-center" style="text-decoration: none; color: inherit;">
                                <div class="card-body">
                                    <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="var(--rojo)" stroke-width="2"/>
                                            <line x1="16" y1="2" x2="16" y2="6" stroke="var(--rojo)" stroke-width="2"/>
                                            <line x1="8" y1="2" x2="8" y2="6" stroke="var(--rojo)" stroke-width="2"/>
                                            <line x1="3" y1="10" x2="21" y2="10" stroke="var(--rojo)" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4>Ver Reservas</h4>
                                    <p class="text-sm text-gris-600">Gestiona las reservas</p>
                                </div>
                            </a>
                            
                            <a href="canchas.html" class="card text-center" style="text-decoration: none; color: inherit;">
                                <div class="card-body">
                                    <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <h4>Editar Canchas</h4>
                                    <p class="text-sm text-gris-600">Actualiza información</p>
                                </div>
                            </a>
                            
                            <a href="../perfil.html" class="card text-center" style="text-decoration: none; color: inherit;">
                                <div class="card-body">
                                    <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="var(--rojo)" stroke-width="2"/>
                                            <circle cx="12" cy="7" r="4" stroke="var(--rojo)" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4>Mi Perfil</h4>
                                    <p class="text-sm text-gris-600">Configurar cuenta</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Información de Ayuda -->
                <div class="card" style="margin-top: var(--espacio-xl);">
                    <div class="card-header">
                        <h3>Información y Ayuda</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-1 grid-cols-md-2 gap-6">
                            <div>
                                <h4>Primeros pasos</h4>
                                <ul style="list-style: none; padding: 0; margin: var(--espacio-sm) 0;">
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">1.</span> Agrega tu primera cancha con fotos y descripción
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">2.</span> Configura horarios y precios
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">3.</span> Publica tu cancha para que los usuarios la encuentren
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">4.</span> Gestiona las reservas desde este panel
                                    </li>
                                </ul>
                            </div>
                            
                            <div>
                                <h4>Consejos para más reservas</h4>
                                <ul style="list-style: none; padding: 0; margin: var(--espacio-sm) 0;">
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">•</span> Sube fotos de alta calidad de tu cancha
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">•</span> Mantén actualizada la disponibilidad
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">•</span> Responde rápido a las consultas
                                    </li>
                                    <li style="margin-bottom: var(--espacio-xs);">
                                        <span class="text-rojo">•</span> Ofrece servicios adicionales (estacionamiento, camarines)
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL Admin</h3>
                    <p>Panel de administración para dueños de canchas deportivas.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Soporte</h3>
                    <ul>
                        <li><a href="#">Centro de Ayuda</a></li>
                        <li><a href="#">Contactar Soporte</a></li>
                        <li><a href="#">Guías y Tutoriales</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos de Servicio</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Comisiones y Tarifas</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Panel Administrativo.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../assets/js/app.js" defer></script>
    <script src="../../assets/js/api.js" defer></script>
    <script src="../../assets/js/ui.js" defer></script>
    <script>
        let usuario = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Verificar autenticación y permisos
            const token = localStorage.getItem('token');
            const usuarioData = localStorage.getItem('usuario');
            
            if (!token || !usuarioData) {
                window.location.href = '../login.html?redirect=admin/dashboard.html';
                return;
            }
            
            try {
                usuario = JSON.parse(usuarioData);
                
                // Verificar que sea admin o dueño
                if (usuario.tipo !== 'admin' && usuario.tipo !== 'dueno') {
                    alert('No tienes permisos para acceder al panel administrativo.');
                    window.location.href = '../perfil.html';
                    return;
                }
                
                inicializarDashboard();
                cargarEstadisticas();
                cargarReservasRecientes();
                cargarCanchasPopulares();
                
            } catch (error) {
                console.error('Error parsing user data:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('usuario');
                window.location.href = '../login.html';
            }
        });

        function inicializarDashboard() {
            // Mostrar nombre del admin
            document.getElementById('admin-name-mobile').textContent = usuario.nombre;
            
            // Configurar logout
            document.getElementById('logout-desktop').addEventListener('click', logout);
            document.getElementById('logout-mobile').addEventListener('click', logout);
        }

        function cargarEstadisticas() {
            // Simular estadísticas (en producción vendría de la API)
            setTimeout(() => {
                document.getElementById('total-canchas').textContent = '8';
                document.getElementById('reservas-mes').textContent = '47';
                document.getElementById('ingresos-mes').textContent = '$890.000';
                document.getElementById('ocupacion-promedio').textContent = '73%';
            }, 500);
        }

        function cargarReservasRecientes() {
            const loadingElement = document.getElementById('reservas-recientes-loading');
            const listElement = document.getElementById('reservas-recientes-list');
            const noReservasElement = document.getElementById('no-reservas-recientes');
            
            if (window.API) {
                window.API.getReservas().then(response => {
                    loadingElement.style.display = 'none';
                    
                    if (response.success && response.data.length > 0) {
                        listElement.style.display = 'block';
                        renderReservasRecientes(response.data.slice(0, 5)); // Solo las 5 más recientes
                    } else {
                        noReservasElement.style.display = 'block';
                    }
                }).catch(error => {
                    console.error('Error cargando reservas:', error);
                    loadingElement.style.display = 'none';
                    noReservasElement.style.display = 'block';
                });
            }
        }

        function renderReservasRecientes(reservas) {
            const container = document.getElementById('reservas-recientes-list');
            container.innerHTML = '';
            
            reservas.forEach(reserva => {
                const reservaItem = document.createElement('div');
                reservaItem.style.cssText = 'padding: var(--espacio-sm) 0; border-bottom: 1px solid var(--gris-200);';
                reservaItem.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">${reserva.cancha?.nombre || 'Cancha'}</p>
                            <p class="text-sm text-gris-600">${formatearFecha(reserva.fecha)} - ${reserva.hora}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">${reserva.totalFormateado}</p>
                            <div class="chip chip-${reserva.estado === 'confirmada' ? 'disponible' : 'pocas'} text-xs">
                                ${reserva.estado}
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(reservaItem);
            });
        }

        function cargarCanchasPopulares() {
            const loadingElement = document.getElementById('canchas-populares-loading');
            const listElement = document.getElementById('canchas-populares-list');
            const noCanchasElement = document.getElementById('no-canchas-populares');
            
            // Simular datos de canchas populares
            setTimeout(() => {
                loadingElement.style.display = 'none';
                listElement.style.display = 'block';
                
                const canchasPopulares = [
                    { nombre: 'Estadio Municipal Las Condes', reservas: 23, rating: 4.8 },
                    { nombre: 'Club de Tenis Providencia', reservas: 18, rating: 4.7 },
                    { nombre: 'Complejo Deportivo Maipú', reservas: 15, rating: 4.5 },
                    { nombre: 'Pádel Center Ñuñoa', reservas: 12, rating: 4.3 }
                ];
                
                renderCanchasPopulares(canchasPopulares);
            }, 800);
        }

        function renderCanchasPopulares(canchas) {
            const container = document.getElementById('canchas-populares-list');
            container.innerHTML = '';
            
            canchas.forEach((cancha, index) => {
                const canchaItem = document.createElement('div');
                canchaItem.style.cssText = 'padding: var(--espacio-sm) 0; border-bottom: 1px solid var(--gris-200);';
                canchaItem.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">${cancha.nombre}</p>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gris-600">${cancha.reservas} reservas</span>
                                <div class="flex items-center gap-1">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="var(--rojo)" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                                    </svg>
                                    <span class="text-sm">${cancha.rating}</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="text-2xl font-bold text-rojo">#${index + 1}</span>
                        </div>
                    </div>
                `;
                container.appendChild(canchaItem);
            });
        }

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('usuario');
            window.location.href = '../index.html';
        }

        function formatearFecha(fecha) {
            const date = new Date(fecha + 'T00:00:00');
            return date.toLocaleDateString('es-CL', {
                day: 'numeric',
                month: 'short'
            });
        }
    </script>
</body>
</html>
