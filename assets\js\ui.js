/**
 * CanchaCL - UI Utilities
 * Funciones para manejo de interfaz de usuario
 */

class UIManager {
    constructor() {
        this.modals = new Map();
        this.notifications = [];
        this.init();
    }

    /**
     * Inicializa el gestor de UI
     */
    init() {
        this.setupGlobalEventListeners();
        this.setupMobileMenu();
        this.setupModals();
        this.setupNotifications();
    }

    /**
     * Configura event listeners globales
     */
    setupGlobalEventListeners() {
        // Manejar formularios con loading states
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('loading-form')) {
                this.setFormLoading(form, true);
            }
        });

        // Manejar clicks en enlaces de navegación
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-navigate]')) {
                e.preventDefault();
                const url = e.target.getAttribute('data-navigate');
                this.navigate(url);
            }
        });

        // Manejar escape key para cerrar modales
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });
    }

    /**
     * Configura el menú móvil
     */
    setupMobileMenu() {
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');

        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', () => {
                const isOpen = mobileMenu.classList.contains('active');
                
                if (isOpen) {
                    mobileMenu.classList.remove('active');
                    menuToggle.setAttribute('aria-expanded', 'false');
                } else {
                    mobileMenu.classList.add('active');
                    menuToggle.setAttribute('aria-expanded', 'true');
                }
            });

            // Cerrar menú al hacer click fuera
            document.addEventListener('click', (e) => {
                if (!menuToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
                    mobileMenu.classList.remove('active');
                    menuToggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    }

    /**
     * Configura modales
     */
    setupModals() {
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    this.closeModal(backdrop.id);
                }
            });
        });

        document.querySelectorAll('.modal-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal-backdrop');
                if (modal) {
                    this.closeModal(modal.id);
                }
            });
        });
    }

    /**
     * Configura sistema de notificaciones
     */
    setupNotifications() {
        // Crear contenedor de notificaciones si no existe
        if (!document.getElementById('notifications-container')) {
            const container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }

    /**
     * Renderiza tarjetas de canchas
     * @param {Array} canchas - Array de canchas
     * @param {string} containerId - ID del contenedor
     */
    renderCanchaCards(canchas, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';

        canchas.forEach(cancha => {
            const card = this.createCanchaCard(cancha);
            container.appendChild(card);
        });
    }

    /**
     * Crea una tarjeta de cancha
     * @param {Object} cancha - Datos de la cancha
     * @returns {HTMLElement} Elemento de la tarjeta
     */
    createCanchaCard(cancha) {
        const card = document.createElement('div');
        card.className = 'cancha-card';
        
        const disponibilidadClass = cancha.disponibilidad === 'disponible' ? 'chip-disponible' : 
                                   cancha.disponibilidad === 'pocas' ? 'chip-pocas' : 'chip-agotado';
        
        const disponibilidadText = cancha.disponibilidad === 'disponible' ? 'Disponible' : 
                                  cancha.disponibilidad === 'pocas' ? 'Pocas disponibles' : 'Agotado';

        card.innerHTML = `
            <div class="cancha-card-image">
                <img src="${cancha.fotos?.[0] || '/assets/img/placeholder.jpg'}" 
                     alt="${cancha.nombre}" 
                     loading="lazy">
                <div class="cancha-card-badge">
                    <div class="chip ${disponibilidadClass}">${disponibilidadText}</div>
                </div>
            </div>
            <div class="cancha-card-content">
                <div class="cancha-card-header">
                    <h3 class="cancha-card-title">${cancha.nombre}</h3>
                    <div class="cancha-card-rating">
                        <div class="flex items-center gap-1">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="var(--rojo)" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                            </svg>
                            <span class="font-semibold">${cancha.rating}</span>
                        </div>
                        <span class="text-sm text-gris-600">(${cancha.totalReviews})</span>
                    </div>
                </div>
                
                <div class="cancha-card-info">
                    <div class="cancha-card-location">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 10C21 17L12 23L3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span>${cancha.comuna}</span>
                    </div>
                    
                    <div class="cancha-card-sport">
                        <div class="chip chip-deporte">${this.formatearDeporte(cancha.deporte)}</div>
                    </div>
                </div>
                
                <div class="cancha-card-details">
                    <span class="text-sm text-gris-600">${cancha.superficie}</span>
                    <div class="cancha-card-price">${cancha.precioFormateado || this.formatearPrecio(cancha.precioHoraCLP)}/hora</div>
                </div>
                
                <div class="cancha-card-actions">
                    <a href="cancha.html?id=${cancha.id}" class="btn btn-outline btn-sm">Ver detalles</a>
                    <a href="reserva.html?cancha=${cancha.id}" class="btn btn-primary btn-sm">Reservar</a>
                </div>
            </div>
        `;

        return card;
    }

    /**
     * Muestra un modal
     * @param {string} modalId - ID del modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            const modalContent = modal.querySelector('.modal');
            if (modalContent) {
                modalContent.classList.add('active');
            }
            this.modals.set(modalId, true);
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Cierra un modal
     * @param {string} modalId - ID del modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            const modalContent = modal.querySelector('.modal');
            if (modalContent) {
                modalContent.classList.remove('active');
            }
            this.modals.delete(modalId);
            
            // Restaurar scroll si no hay más modales abiertos
            if (this.modals.size === 0) {
                document.body.style.overflow = '';
            }
        }
    }

    /**
     * Cierra el modal superior
     */
    closeTopModal() {
        const openModals = Array.from(this.modals.keys());
        if (openModals.length > 0) {
            this.closeModal(openModals[openModals.length - 1]);
        }
    }

    /**
     * Muestra una notificación
     * @param {string} message - Mensaje a mostrar
     * @param {string} type - Tipo de notificación (success, error, warning, info)
     * @param {number} duration - Duración en ms (0 = permanente)
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        if (!container) return;

        const notification = document.createElement('div');
        const id = this.generateId();
        
        notification.id = `notification-${id}`;
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: white;
            border-left: 4px solid var(--${type === 'success' ? 'verde' : type === 'error' ? 'rojo' : type === 'warning' ? 'amarillo' : 'azul'});
            border-radius: var(--radio);
            box-shadow: var(--shadow-lg);
            padding: var(--espacio-md);
            margin-bottom: var(--espacio-sm);
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideInRight 0.3s ease;
        `;

        notification.innerHTML = `
            <div class="notification-content">
                <p>${message}</p>
            </div>
            <button class="notification-close" onclick="window.UI.closeNotification('${id}')">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        `;

        container.appendChild(notification);
        this.notifications.push(id);

        // Auto-cerrar si tiene duración
        if (duration > 0) {
            setTimeout(() => {
                this.closeNotification(id);
            }, duration);
        }

        return id;
    }

    /**
     * Cierra una notificación
     * @param {string} id - ID de la notificación
     */
    closeNotification(id) {
        const notification = document.getElementById(`notification-${id}`);
        if (notification) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                notification.remove();
                this.notifications = this.notifications.filter(n => n !== id);
            }, 300);
        }
    }

    /**
     * Establece el estado de carga de un formulario
     * @param {HTMLFormElement} form - Formulario
     * @param {boolean} loading - Estado de carga
     */
    setFormLoading(form, loading) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea');

        if (loading) {
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.dataset.originalText = submitBtn.textContent;
                submitBtn.innerHTML = '<div class="loading" style="margin-right: var(--espacio-sm);"></div>Cargando...';
            }
            inputs.forEach(input => input.disabled = true);
        } else {
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = submitBtn.dataset.originalText || 'Enviar';
            }
            inputs.forEach(input => input.disabled = false);
        }
    }

    /**
     * Navega a una URL
     * @param {string} url - URL de destino
     */
    navigate(url) {
        window.location.href = url;
    }

    /**
     * Formatea un deporte para mostrar
     * @param {string} deporte - Código del deporte
     * @returns {string} Nombre formateado
     */
    formatearDeporte(deporte) {
        const deportes = {
            'futbol': 'Fútbol',
            'tenis': 'Tenis',
            'padel': 'Pádel',
            'basquet': 'Básquetbol',
            'baby_futbol': 'Baby Fútbol'
        };
        return deportes[deporte] || deporte;
    }

    /**
     * Formatea un precio
     * @param {number} precio - Precio a formatear
     * @returns {string} Precio formateado
     */
    formatearPrecio(precio) {
        return '$' + precio.toLocaleString('es-CL');
    }

    /**
     * Genera un ID único
     * @returns {string} ID único
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Scroll suave a un elemento
     * @param {string} selector - Selector del elemento
     * @param {number} offset - Offset en pixels
     */
    scrollTo(selector, offset = 0) {
        const element = document.querySelector(selector);
        if (element) {
            const top = element.offsetTop - offset;
            window.scrollTo({
                top: top,
                behavior: 'smooth'
            });
        }
    }

    /**
     * Lazy loading de imágenes
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Valida un formulario
     * @param {HTMLFormElement} form - Formulario a validar
     * @returns {boolean} True si es válido
     */
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'Este campo es requerido');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        return isValid;
    }

    /**
     * Muestra error en un campo
     * @param {HTMLElement} field - Campo del formulario
     * @param {string} message - Mensaje de error
     */
    showFieldError(field, message) {
        field.classList.add('error');
        
        let errorElement = field.parentNode.querySelector('.form-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'form-error';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    }

    /**
     * Limpia error de un campo
     * @param {HTMLElement} field - Campo del formulario
     */
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.form-error');
        if (errorElement) {
            errorElement.textContent = '';
        }
    }
}

// Crear instancia global
window.UI = new UIManager();

// Inicializar cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.UI.setupLazyLoading();
    });
} else {
    window.UI.setupLazyLoading();
}

// Agregar estilos CSS para notificaciones y animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        color: var(--gris-600);
        transition: color 0.2s ease;
    }

    .notification-close:hover {
        color: var(--negro);
    }
`;
document.head.appendChild(style);
