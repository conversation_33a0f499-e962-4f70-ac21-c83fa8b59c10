<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Función para cargar datos mock
function loadMockData($filename) {
    $filepath = __DIR__ . '/../assets/mock/' . $filename;
    if (!file_exists($filepath)) {
        return [];
    }
    $content = file_get_contents($filepath);
    return json_decode($content, true) ?: [];
}

// Función para formatear precio chileno
function formatPrecioCLP($precio) {
    return '$' . number_format($precio, 0, ',', '.');
}

// Función para generar ID único mock
function generarIdMock() {
    return rand(10000, 99999);
}

// Función para validar token mock
function validarToken($token) {
    if (empty($token)) {
        return false;
    }
    
    try {
        $decoded = json_decode(base64_decode($token), true);
        if (!$decoded || !isset($decoded['userId']) || !isset($decoded['expires'])) {
            return false;
        }
        
        // Verificar si el token ha expirado
        if ($decoded['expires'] < time()) {
            return false;
        }
        
        return $decoded['userId'];
    } catch (Exception $e) {
        return false;
    }
}

// Reservas mock (en producción esto estaría en base de datos)
$reservasMock = [
    [
        'id' => 1,
        'usuarioId' => 1,
        'canchaId' => 1,
        'fecha' => '2024-08-20',
        'hora' => '18:00',
        'duracion' => 1,
        'totalCLP' => 25000,
        'estado' => 'confirmada',
        'fechaReserva' => '2024-08-19 10:30:00',
        'metodoPago' => 'tarjeta',
        'notas' => 'Partido de fútbol amateur'
    ],
    [
        'id' => 2,
        'usuarioId' => 1,
        'canchaId' => 3,
        'fecha' => '2024-08-22',
        'hora' => '16:00',
        'duracion' => 1,
        'totalCLP' => 15000,
        'estado' => 'pendiente',
        'fechaReserva' => '2024-08-19 14:15:00',
        'metodoPago' => 'transferencia',
        'notas' => 'Clase de tenis'
    ]
];

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Obtener token de autorización
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    $token = str_replace('Bearer ', '', $authHeader);
    
    if ($method === 'GET') {
        // Listar reservas
        $usuarioId = $_GET['usuario_id'] ?? null;
        $canchaId = $_GET['cancha_id'] ?? null;
        $estado = $_GET['estado'] ?? null;
        
        $reservas = $reservasMock;
        
        // Filtrar por usuario
        if ($usuarioId) {
            $reservas = array_filter($reservas, function($r) use ($usuarioId) {
                return $r['usuarioId'] == $usuarioId;
            });
        }
        
        // Filtrar por cancha
        if ($canchaId) {
            $reservas = array_filter($reservas, function($r) use ($canchaId) {
                return $r['canchaId'] == $canchaId;
            });
        }
        
        // Filtrar por estado
        if ($estado) {
            $reservas = array_filter($reservas, function($r) use ($estado) {
                return $r['estado'] === $estado;
            });
        }
        
        // Cargar información de canchas
        $canchas = loadMockData('canchas.json');
        $canchasById = [];
        foreach ($canchas as $cancha) {
            $canchasById[$cancha['id']] = $cancha;
        }
        
        // Enriquecer reservas con información de cancha
        $reservasEnriquecidas = array_map(function($reserva) use ($canchasById) {
            $reserva['cancha'] = $canchasById[$reserva['canchaId']] ?? null;
            $reserva['totalFormateado'] = formatPrecioCLP($reserva['totalCLP']);
            return $reserva;
        }, $reservas);
        
        // Ordenar por fecha de reserva descendente
        usort($reservasEnriquecidas, function($a, $b) {
            return strtotime($b['fechaReserva']) - strtotime($a['fechaReserva']);
        });
        
        echo json_encode([
            'success' => true,
            'data' => array_values($reservasEnriquecidas)
        ]);
        
    } elseif ($method === 'POST') {
        // Crear nueva reserva
        $usuarioId = validarToken($token);
        if (!$usuarioId) {
            http_response_code(401);
            echo json_encode([
                'error' => true,
                'message' => 'Token inválido o expirado'
            ]);
            exit();
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Datos inválidos'
            ]);
            exit();
        }
        
        $canchaId = intval($input['canchaId'] ?? 0);
        $fecha = $input['fecha'] ?? '';
        $hora = $input['hora'] ?? '';
        $duracion = intval($input['duracion'] ?? 1);
        $metodoPago = $input['metodoPago'] ?? '';
        $notas = trim($input['notas'] ?? '');
        
        // Validaciones
        $errores = [];
        
        if ($canchaId <= 0) {
            $errores[] = 'ID de cancha inválido';
        }
        
        if (empty($fecha)) {
            $errores[] = 'La fecha es requerida';
        } elseif (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha)) {
            $errores[] = 'Formato de fecha inválido (YYYY-MM-DD)';
        }
        
        if (empty($hora)) {
            $errores[] = 'La hora es requerida';
        } elseif (!preg_match('/^\d{2}:\d{2}$/', $hora)) {
            $errores[] = 'Formato de hora inválido (HH:MM)';
        }
        
        if ($duracion < 1 || $duracion > 4) {
            $errores[] = 'La duración debe ser entre 1 y 4 horas';
        }
        
        if (!in_array($metodoPago, ['tarjeta', 'transferencia', 'efectivo'])) {
            $errores[] = 'Método de pago inválido';
        }
        
        if (!empty($errores)) {
            http_response_code(400);
            echo json_encode([
                'error' => true,
                'message' => 'Errores de validación',
                'errores' => $errores
            ]);
            exit();
        }
        
        // Verificar que la cancha existe
        $canchas = loadMockData('canchas.json');
        $cancha = null;
        foreach ($canchas as $c) {
            if ($c['id'] === $canchaId) {
                $cancha = $c;
                break;
            }
        }
        
        if (!$cancha) {
            http_response_code(404);
            echo json_encode([
                'error' => true,
                'message' => 'Cancha no encontrada'
            ]);
            exit();
        }
        
        // Calcular total
        $total = $cancha['precioHoraCLP'] * $duracion;
        
        // Crear reserva
        $nuevaReserva = [
            'id' => generarIdMock(),
            'usuarioId' => $usuarioId,
            'canchaId' => $canchaId,
            'fecha' => $fecha,
            'hora' => $hora,
            'duracion' => $duracion,
            'totalCLP' => $total,
            'estado' => 'confirmada',
            'fechaReserva' => date('Y-m-d H:i:s'),
            'metodoPago' => $metodoPago,
            'notas' => $notas,
            'cancha' => $cancha,
            'totalFormateado' => formatPrecioCLP($total)
        ];
        
        echo json_encode([
            'success' => true,
            'message' => 'Reserva creada exitosamente',
            'data' => $nuevaReserva
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Método no permitido'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
