<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalle de Cancha - CanchaCL</title>
    <meta name="description" content="Información detallada de la cancha deportiva. Horarios, precios, servicios y reservas.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="#" id="nav-login">Iniciar Sesión</a></li>
                            <li><a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="#" id="nav-login-mobile" class="btn btn-ghost btn-sm">Entrar</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="perfil.html">Mi Perfil</a></li>
                <li><a href="admin/dashboard.html">Panel Admin</a></li>
                <li><a href="#" id="logout-link">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Loading State -->
        <div id="loading-state" class="text-center" style="padding: var(--espacio-3xl) 0;">
            <div class="loading" style="margin: 0 auto var(--espacio-md);"></div>
            <p>Cargando información de la cancha...</p>
        </div>

        <!-- Cancha Content -->
        <div id="cancha-content" style="display: none;">
            <!-- Breadcrumb -->
            <section class="section" style="padding-top: var(--espacio-lg); padding-bottom: 0;">
                <div class="container">
                    <nav aria-label="breadcrumb">
                        <ol style="display: flex; gap: var(--espacio-sm); list-style: none; padding: 0; margin: 0; font-size: var(--font-size-sm); color: var(--gris-600);">
                            <li><a href="index.html" style="color: var(--rojo);">Inicio</a></li>
                            <li>/</li>
                            <li><a href="resultados.html" style="color: var(--rojo);">Buscar Canchas</a></li>
                            <li>/</li>
                            <li id="breadcrumb-cancha">Cancha</li>
                        </ol>
                    </nav>
                </div>
            </section>

            <!-- Cancha Header -->
            <section class="section">
                <div class="container">
                    <div class="cancha-header">
                        <h1 class="cancha-title" id="cancha-titulo">Nombre de la Cancha</h1>
                        <div class="cancha-location" id="cancha-ubicacion">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 10C21 17L12 23L3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            Dirección de la cancha
                        </div>
                        <div class="cancha-rating-section">
                            <div class="cancha-card-rating" id="cancha-rating">
                                <div class="flex items-center gap-1">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="var(--rojo)" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                                    </svg>
                                    <span class="font-semibold">4.5</span>
                                </div>
                                <span class="text-sm text-gris-600">(123 reseñas)</span>
                            </div>
                            <div class="chip" id="cancha-disponibilidad">Disponible</div>
                            <div class="cancha-card-price" id="cancha-precio">$15.000/hora</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Gallery -->
            <section class="section" style="padding-top: 0;">
                <div class="container">
                    <div class="cancha-gallery">
                        <img id="gallery-main" class="gallery-main" src="/assets/img/placeholder.jpg" alt="Imagen principal de la cancha">
                        <div class="gallery-thumbs" id="gallery-thumbs">
                            <!-- Las miniaturas se cargan dinámicamente -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Info and Booking -->
            <section class="section" style="padding-top: 0;">
                <div class="container">
                    <div class="cancha-info">
                        <!-- Left Column - Info -->
                        <div>
                            <!-- Description -->
                            <div class="info-section">
                                <h3 class="info-title">Descripción</h3>
                                <p id="cancha-descripcion">Descripción de la cancha...</p>
                            </div>

                            <!-- Services -->
                            <div class="info-section">
                                <h3 class="info-title">Servicios incluidos</h3>
                                <div class="services-grid" id="cancha-servicios">
                                    <!-- Los servicios se cargan dinámicamente -->
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="info-section">
                                <h3 class="info-title">Detalles</h3>
                                <div class="grid grid-cols-1 grid-cols-sm-2 gap-4">
                                    <div>
                                        <strong>Deporte:</strong>
                                        <span id="cancha-deporte">Fútbol</span>
                                    </div>
                                    <div>
                                        <strong>Superficie:</strong>
                                        <span id="cancha-superficie">Pasto natural</span>
                                    </div>
                                    <div>
                                        <strong>Comuna:</strong>
                                        <span id="cancha-comuna">Santiago</span>
                                    </div>
                                    <div>
                                        <strong>Teléfono:</strong>
                                        <span id="cancha-telefono">+56 2 2234 5678</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Map Placeholder -->
                            <div class="info-section">
                                <h3 class="info-title">Ubicación</h3>
                                <div style="width: 100%; height: 200px; background-color: var(--gris-100); border-radius: var(--radio); display: flex; align-items: center; justify-content: center; color: var(--gris-600);">
                                    <div class="text-center">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 0 auto var(--espacio-sm);">
                                            <path d="M21 10C21 17L12 23L3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <p>Mapa de ubicación</p>
                                        <p class="text-sm" id="cancha-direccion-completa">Dirección completa</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column - Booking -->
                        <div>
                            <div class="info-section" style="position: sticky; top: calc(64px + var(--espacio-lg));">
                                <h3 class="info-title">Reservar cancha</h3>
                                
                                <form id="booking-form">
                                    <div class="form-group">
                                        <label for="booking-fecha" class="form-label">Fecha</label>
                                        <input type="date" id="booking-fecha" name="fecha" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Horarios disponibles</label>
                                        <div class="horarios-grid" id="horarios-disponibles">
                                            <!-- Los horarios se cargan dinámicamente -->
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="booking-duracion" class="form-label">Duración</label>
                                        <select id="booking-duracion" name="duracion" class="form-input form-select" required>
                                            <option value="1">1 hora</option>
                                            <option value="2">2 horas</option>
                                            <option value="3">3 horas</option>
                                            <option value="4">4 horas</option>
                                        </select>
                                    </div>
                                    
                                    <div class="resumen-reserva" id="resumen-reserva" style="display: none;">
                                        <h4>Resumen de reserva</h4>
                                        <div class="resumen-item">
                                            <span>Fecha:</span>
                                            <span id="resumen-fecha">-</span>
                                        </div>
                                        <div class="resumen-item">
                                            <span>Hora:</span>
                                            <span id="resumen-hora">-</span>
                                        </div>
                                        <div class="resumen-item">
                                            <span>Duración:</span>
                                            <span id="resumen-duracion">-</span>
                                        </div>
                                        <div class="resumen-item">
                                            <span>Total:</span>
                                            <span id="resumen-total">-</span>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-full btn-lg" id="btn-reservar">
                                        Continuar con la reserva
                                    </button>
                                </form>
                                
                                <div class="text-center" style="margin-top: var(--espacio-md);">
                                    <p class="text-sm text-gris-600">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle; margin-right: var(--espacio-xs);">
                                            <path d="M9 12L11 14L15 10M21 12C21 16.971 16.971 21 12 21S3 16.971 3 12 7.029 3 12 3 21 7.029 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Reserva segura y confirmación inmediata
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Error State -->
        <div id="error-state" class="text-center" style="padding: var(--espacio-3xl) 0; display: none;">
            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-lg); opacity: 0.5;">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="var(--gris-400)" stroke-width="2"/>
                    <path d="M15 9L9 15M9 9L15 15" stroke="var(--gris-400)" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <h3>Cancha no encontrada</h3>
            <p>La cancha que buscas no existe o ha sido eliminada.</p>
            <a href="resultados.html" class="btn btn-primary">Buscar otras canchas</a>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script src="../assets/js/validators.js" defer></script>
    <script>
        // Variables globales
        let canchaActual = null;
        let horaSeleccionada = null;

        // Inicializar página de detalle
        document.addEventListener('DOMContentLoaded', function() {
            // Obtener ID de la cancha desde URL
            const urlParams = new URLSearchParams(window.location.search);
            const canchaId = urlParams.get('id');
            
            if (!canchaId) {
                mostrarError();
                return;
            }
            
            cargarCancha(canchaId);
            
            // Establecer fecha mínima como hoy
            const fechaInput = document.getElementById('booking-fecha');
            const hoy = new Date().toISOString().split('T')[0];
            fechaInput.min = hoy;
            fechaInput.value = hoy;
            
            // Event listeners
            fechaInput.addEventListener('change', cargarHorarios);
            document.getElementById('booking-duracion').addEventListener('change', actualizarResumen);
        });

        // Función para cargar información de la cancha
        function cargarCancha(id) {
            if (window.API) {
                window.API.getCancha(id).then(response => {
                    document.getElementById('loading-state').style.display = 'none';
                    
                    if (response.success && response.data) {
                        canchaActual = response.data;
                        mostrarCancha(response.data);
                        cargarHorarios();
                    } else {
                        mostrarError();
                    }
                }).catch(error => {
                    console.error('Error cargando cancha:', error);
                    document.getElementById('loading-state').style.display = 'none';
                    mostrarError();
                });
            }
        }

        // Función para mostrar información de la cancha
        function mostrarCancha(cancha) {
            document.getElementById('cancha-content').style.display = 'block';
            
            // Actualizar título de página
            document.title = `${cancha.nombre} - CanchaCL`;
            
            // Llenar información básica
            document.getElementById('breadcrumb-cancha').textContent = cancha.nombre;
            document.getElementById('cancha-titulo').textContent = cancha.nombre;
            document.getElementById('cancha-ubicacion').innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 10C21 17L12 23L3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                ${cancha.direccion}
            `;
            
            // Rating y disponibilidad
            document.getElementById('cancha-rating').innerHTML = `
                <div class="flex items-center gap-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="var(--rojo)" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                    </svg>
                    <span class="font-semibold">${cancha.rating}</span>
                </div>
                <span class="text-sm text-gris-600">(${cancha.totalReviews} reseñas)</span>
            `;
            
            const disponibilidadChip = document.getElementById('cancha-disponibilidad');
            disponibilidadChip.className = `chip chip-${cancha.disponibilidad}`;
            disponibilidadChip.textContent = cancha.disponibilidad === 'disponible' ? 'Disponible' : 
                                           cancha.disponibilidad === 'pocas' ? 'Pocas' : 'Agotado';
            
            document.getElementById('cancha-precio').textContent = cancha.precioFormateado + '/hora';
            
            // Galería
            if (cancha.fotos && cancha.fotos.length > 0) {
                const mainImage = document.getElementById('gallery-main');
                mainImage.src = cancha.fotos[0];
                mainImage.alt = `Imagen principal de ${cancha.nombre}`;
                
                const thumbsContainer = document.getElementById('gallery-thumbs');
                thumbsContainer.innerHTML = '';
                cancha.fotos.forEach((foto, index) => {
                    const thumb = document.createElement('img');
                    thumb.src = foto;
                    thumb.alt = `Imagen ${index + 1} de ${cancha.nombre}`;
                    thumb.className = `gallery-thumb ${index === 0 ? 'active' : ''}`;
                    thumb.onclick = () => {
                        mainImage.src = foto;
                        document.querySelectorAll('.gallery-thumb').forEach(t => t.classList.remove('active'));
                        thumb.classList.add('active');
                    };
                    thumbsContainer.appendChild(thumb);
                });
            }
            
            // Información detallada
            document.getElementById('cancha-descripcion').textContent = cancha.descripcion || 'Sin descripción disponible.';
            document.getElementById('cancha-deporte').textContent = formatearDeporte(cancha.deporte);
            document.getElementById('cancha-superficie').textContent = cancha.superficie;
            document.getElementById('cancha-comuna').textContent = cancha.comuna;
            document.getElementById('cancha-telefono').textContent = cancha.telefono || 'No disponible';
            document.getElementById('cancha-direccion-completa').textContent = cancha.direccion;
            
            // Servicios
            const serviciosContainer = document.getElementById('cancha-servicios');
            serviciosContainer.innerHTML = '';
            if (cancha.servicios && cancha.servicios.length > 0) {
                cancha.servicios.forEach(servicio => {
                    const serviceItem = document.createElement('div');
                    serviceItem.className = 'service-item';
                    serviceItem.innerHTML = `
                        <svg class="service-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.971 16.971 21 12 21S3 16.971 3 12 7.029 3 12 3 21 7.029 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>${servicio}</span>
                    `;
                    serviciosContainer.appendChild(serviceItem);
                });
            }
        }

        // Función para cargar horarios disponibles
        function cargarHorarios() {
            if (!canchaActual) return;
            
            const horariosContainer = document.getElementById('horarios-disponibles');
            horariosContainer.innerHTML = '';
            
            if (canchaActual.horariosHoy) {
                canchaActual.horariosHoy.forEach(horario => {
                    const slot = document.createElement('button');
                    slot.type = 'button';
                    slot.className = `horario-slot ${!horario.disponible ? 'disabled' : ''}`;
                    slot.textContent = horario.hora;
                    slot.disabled = !horario.disponible;
                    
                    if (horario.disponible) {
                        slot.onclick = () => seleccionarHora(horario.hora, slot);
                    }
                    
                    horariosContainer.appendChild(slot);
                });
            }
        }

        // Función para seleccionar hora
        function seleccionarHora(hora, elemento) {
            // Remover selección anterior
            document.querySelectorAll('.horario-slot').forEach(slot => {
                slot.classList.remove('selected');
            });
            
            // Seleccionar nueva hora
            elemento.classList.add('selected');
            horaSeleccionada = hora;
            
            actualizarResumen();
        }

        // Función para actualizar resumen de reserva
        function actualizarResumen() {
            const fecha = document.getElementById('booking-fecha').value;
            const duracion = document.getElementById('booking-duracion').value;
            const resumenContainer = document.getElementById('resumen-reserva');
            
            if (fecha && horaSeleccionada && duracion && canchaActual) {
                const total = canchaActual.precioHoraCLP * parseInt(duracion);
                
                document.getElementById('resumen-fecha').textContent = formatearFecha(fecha);
                document.getElementById('resumen-hora').textContent = horaSeleccionada;
                document.getElementById('resumen-duracion').textContent = `${duracion} hora${duracion > 1 ? 's' : ''}`;
                document.getElementById('resumen-total').textContent = formatearPrecio(total);
                
                resumenContainer.style.display = 'block';
                document.getElementById('btn-reservar').disabled = false;
            } else {
                resumenContainer.style.display = 'none';
                document.getElementById('btn-reservar').disabled = true;
            }
        }

        // Función para mostrar error
        function mostrarError() {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('error-state').style.display = 'block';
        }

        // Funciones auxiliares
        function formatearDeporte(deporte) {
            const deportes = {
                'futbol': 'Fútbol',
                'tenis': 'Tenis',
                'padel': 'Pádel',
                'basquet': 'Básquetbol',
                'baby_futbol': 'Baby Fútbol'
            };
            return deportes[deporte] || deporte;
        }

        function formatearFecha(fecha) {
            const date = new Date(fecha + 'T00:00:00');
            return date.toLocaleDateString('es-CL', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function formatearPrecio(precio) {
            return '$' + precio.toLocaleString('es-CL');
        }
    </script>
</body>
</html>
