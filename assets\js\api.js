/**
 * CanchaCL - API Client
 * Maneja todas las comunicaciones con el backend
 */

class APIClient {
    constructor() {
        this.baseURL = window.location.origin;
        this.endpoints = {
            canchas: '/api/canchas.php',
            comunas: '/api/comunas.php',
            login: '/api/auth_login.php',
            registro: '/api/auth_registro.php',
            reservas: '/api/reservas.php'
        };
    }

    /**
     * Realiza una petición HTTP
     * @param {string} url - URL del endpoint
     * @param {Object} options - Opciones de la petición
     * @returns {Promise<Object>} Respuesta de la API
     */
    async request(url, options = {}) {
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Agregar token de autorización si existe
        const token = localStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }

        try {
            const response = await fetch(url, config);
            
            // Verificar si la respuesta es JSON válida
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('La respuesta del servidor no es JSON válida');
            }

            const data = await response.json();

            // Manejar errores HTTP
            if (!response.ok) {
                throw new Error(data.message || `Error HTTP: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('Error en petición API:', error);
            throw error;
        }
    }

    /**
     * Obtiene la lista de canchas con filtros opcionales
     * @param {Object} filtros - Filtros de búsqueda
     * @returns {Promise<Object>} Lista de canchas
     */
    async getCanchas(filtros = {}) {
        const params = new URLSearchParams();
        
        Object.keys(filtros).forEach(key => {
            if (filtros[key]) {
                params.append(key, filtros[key]);
            }
        });

        const url = `${this.baseURL}${this.endpoints.canchas}${params.toString() ? '?' + params.toString() : ''}`;
        return this.request(url);
    }

    /**
     * Obtiene información detallada de una cancha
     * @param {number} id - ID de la cancha
     * @returns {Promise<Object>} Información de la cancha
     */
    async getCancha(id) {
        const url = `${this.baseURL}${this.endpoints.canchas}?id=${id}`;
        return this.request(url);
    }

    /**
     * Obtiene la lista de comunas
     * @param {Object} filtros - Filtros opcionales
     * @returns {Promise<Object>} Lista de comunas
     */
    async getComunas(filtros = {}) {
        const params = new URLSearchParams();
        
        Object.keys(filtros).forEach(key => {
            if (filtros[key]) {
                params.append(key, filtros[key]);
            }
        });

        const url = `${this.baseURL}${this.endpoints.comunas}${params.toString() ? '?' + params.toString() : ''}`;
        return this.request(url);
    }

    /**
     * Inicia sesión de usuario
     * @param {Object} credenciales - Email y contraseña
     * @returns {Promise<Object>} Datos del usuario y token
     */
    async login(credenciales) {
        return this.request(`${this.baseURL}${this.endpoints.login}`, {
            method: 'POST',
            body: JSON.stringify(credenciales)
        });
    }

    /**
     * Registra un nuevo usuario
     * @param {Object} datosUsuario - Datos del nuevo usuario
     * @returns {Promise<Object>} Datos del usuario y token
     */
    async registro(datosUsuario) {
        return this.request(`${this.baseURL}${this.endpoints.registro}`, {
            method: 'POST',
            body: JSON.stringify(datosUsuario)
        });
    }

    /**
     * Obtiene las reservas con filtros opcionales
     * @param {Object} filtros - Filtros de búsqueda
     * @returns {Promise<Object>} Lista de reservas
     */
    async getReservas(filtros = {}) {
        const params = new URLSearchParams();
        
        Object.keys(filtros).forEach(key => {
            if (filtros[key]) {
                params.append(key, filtros[key]);
            }
        });

        const url = `${this.baseURL}${this.endpoints.reservas}${params.toString() ? '?' + params.toString() : ''}`;
        return this.request(url);
    }

    /**
     * Crea una nueva reserva
     * @param {Object} datosReserva - Datos de la reserva
     * @returns {Promise<Object>} Información de la reserva creada
     */
    async crearReserva(datosReserva) {
        return this.request(`${this.baseURL}${this.endpoints.reservas}`, {
            method: 'POST',
            body: JSON.stringify(datosReserva)
        });
    }

    /**
     * Actualiza una reserva existente
     * @param {number} id - ID de la reserva
     * @param {Object} datosReserva - Nuevos datos de la reserva
     * @returns {Promise<Object>} Información de la reserva actualizada
     */
    async actualizarReserva(id, datosReserva) {
        return this.request(`${this.baseURL}${this.endpoints.reservas}`, {
            method: 'PUT',
            body: JSON.stringify({ id, ...datosReserva })
        });
    }

    /**
     * Cancela una reserva
     * @param {number} id - ID de la reserva
     * @returns {Promise<Object>} Confirmación de cancelación
     */
    async cancelarReserva(id) {
        return this.request(`${this.baseURL}${this.endpoints.reservas}`, {
            method: 'DELETE',
            body: JSON.stringify({ id })
        });
    }

    /**
     * Verifica si el usuario está autenticado
     * @returns {boolean} True si está autenticado
     */
    isAuthenticated() {
        const token = localStorage.getItem('token');
        const usuario = localStorage.getItem('usuario');
        return !!(token && usuario);
    }

    /**
     * Obtiene los datos del usuario actual
     * @returns {Object|null} Datos del usuario o null si no está autenticado
     */
    getCurrentUser() {
        try {
            const usuario = localStorage.getItem('usuario');
            return usuario ? JSON.parse(usuario) : null;
        } catch (error) {
            console.error('Error parsing user data:', error);
            return null;
        }
    }

    /**
     * Cierra la sesión del usuario
     */
    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('usuario');
        localStorage.removeItem('remember_login');
    }

    /**
     * Maneja errores de autenticación
     * @param {Error} error - Error de la API
     */
    handleAuthError(error) {
        if (error.message.includes('401') || error.message.includes('token')) {
            this.logout();
            window.location.href = '/public/login.html';
        }
    }
}

// Crear instancia global de la API
window.API = new APIClient();

// Interceptor para manejar errores de autenticación globalmente
const originalRequest = window.API.request;
window.API.request = async function(url, options = {}) {
    try {
        return await originalRequest.call(this, url, options);
    } catch (error) {
        this.handleAuthError(error);
        throw error;
    }
};

// Utilidades adicionales
window.API.utils = {
    /**
     * Formatea un precio en pesos chilenos
     * @param {number} precio - Precio a formatear
     * @returns {string} Precio formateado
     */
    formatearPrecio(precio) {
        return '$' + precio.toLocaleString('es-CL');
    },

    /**
     * Formatea una fecha en formato chileno
     * @param {string} fecha - Fecha en formato ISO
     * @returns {string} Fecha formateada
     */
    formatearFecha(fecha) {
        const date = new Date(fecha + 'T00:00:00');
        return date.toLocaleDateString('es-CL', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    /**
     * Valida un email
     * @param {string} email - Email a validar
     * @returns {boolean} True si es válido
     */
    validarEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },

    /**
     * Valida un teléfono chileno
     * @param {string} telefono - Teléfono a validar
     * @returns {boolean} True si es válido
     */
    validarTelefonoChileno(telefono) {
        const regex = /^\+56\s?[0-9]\s?[0-9]{4}\s?[0-9]{4}$/;
        return regex.test(telefono);
    },

    /**
     * Debounce function para optimizar búsquedas
     * @param {Function} func - Función a ejecutar
     * @param {number} wait - Tiempo de espera en ms
     * @returns {Function} Función con debounce
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Genera un ID único simple
     * @returns {string} ID único
     */
    generarId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};

// Configurar interceptores para desarrollo
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('CanchaCL API Client inicializado en modo desarrollo');
    
    // Log de todas las peticiones en desarrollo
    const originalRequestDev = window.API.request;
    window.API.request = async function(url, options = {}) {
        console.log('API Request:', { url, options });
        try {
            const result = await originalRequestDev.call(this, url, options);
            console.log('API Response:', result);
            return result;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    };
}

// Exportar para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIClient;
}
