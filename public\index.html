<!DOCTYPE html>
<html lang="es-CL">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CanchaCL - Arrienda Canchas Deportivas en Chile</title>
    <meta name="description" content="Encuentra y reserva canchas de fútbol, tenis, pádel, básquet y baby fútbol en Chile. Compara precios y reserva online.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/layout.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    Cancha<span class="accent">CL</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="nav-desktop">
                    <div class="nav-menu">
                        <ul>
                            <li><a href="index.html">Inicio</a></li>
                            <li><a href="resultados.html">Buscar Canchas</a></li>
                            <li><a href="#" id="nav-login">Iniciar Sesión</a></li>
                            <li><a href="registro.html" class="btn btn-primary btn-sm">Registrarse</a></li>
                        </ul>
                    </div>
                </nav>
                
                <!-- Mobile Navigation -->
                <div class="nav-mobile">
                    <a href="#" id="nav-login-mobile" class="btn btn-ghost btn-sm">Entrar</a>
                    <button class="menu-toggle" id="menu-toggle" aria-label="Abrir menú">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <nav class="nav-menu" id="mobile-menu">
            <ul>
                <li><a href="index.html">Inicio</a></li>
                <li><a href="resultados.html">Buscar Canchas</a></li>
                <li><a href="perfil.html">Mi Perfil</a></li>
                <li><a href="admin/dashboard.html">Panel Admin</a></li>
                <li><a href="#" id="logout-link">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <h1 class="hero-title">Encuentra tu cancha perfecta</h1>
                <p class="hero-subtitle">Reserva canchas deportivas en Chile de forma fácil y rápida</p>
                
                <!-- Search Form -->
                <form class="search-form" id="search-form">
                    <div class="search-grid">
                        <div class="form-group">
                            <label for="deporte" class="form-label">Deporte</label>
                            <select id="deporte" name="deporte" class="form-input form-select" required>
                                <option value="">Seleccionar deporte</option>
                                <option value="futbol">Fútbol</option>
                                <option value="tenis">Tenis</option>
                                <option value="padel">Pádel</option>
                                <option value="basquet">Básquetbol</option>
                                <option value="baby_futbol">Baby Fútbol</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="comuna" class="form-label">Comuna</label>
                            <select id="comuna" name="comuna" class="form-input form-select" required>
                                <option value="">Seleccionar comuna</option>
                                <!-- Se llenan dinámicamente -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="fecha" class="form-label">Fecha</label>
                            <input type="date" id="fecha" name="fecha" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="hora" class="form-label">Hora</label>
                            <select id="hora" name="hora" class="form-input form-select">
                                <option value="">Cualquier hora</option>
                                <option value="08:00">08:00</option>
                                <option value="09:00">09:00</option>
                                <option value="10:00">10:00</option>
                                <option value="11:00">11:00</option>
                                <option value="12:00">12:00</option>
                                <option value="13:00">13:00</option>
                                <option value="14:00">14:00</option>
                                <option value="15:00">15:00</option>
                                <option value="16:00">16:00</option>
                                <option value="17:00">17:00</option>
                                <option value="18:00">18:00</option>
                                <option value="19:00">19:00</option>
                                <option value="20:00">20:00</option>
                                <option value="21:00">21:00</option>
                                <option value="22:00">22:00</option>
                                <option value="23:00">23:00</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg search-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Buscar canchas
                        </button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Features Section -->
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">¿Por qué elegir CanchaCL?</h2>
                    <p class="section-subtitle">La plataforma más completa para reservar canchas deportivas en Chile</p>
                </div>
                
                <div class="grid grid-cols-1 grid-cols-md-3 gap-8">
                    <div class="card text-center">
                        <div class="card-body">
                            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-md); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <h3>Búsqueda fácil</h3>
                            <p>Encuentra canchas por deporte, comuna, fecha y horario en segundos.</p>
                        </div>
                    </div>
                    
                    <div class="card text-center">
                        <div class="card-body">
                            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-md); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 12L11 14L15 10M21 12C21 16.971 16.971 21 12 21S3 16.971 3 12 7.029 3 12 3 21 7.029 21 12Z" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <h3>Reserva segura</h3>
                            <p>Sistema de reservas confiable con confirmación inmediata.</p>
                        </div>
                    </div>
                    
                    <div class="card text-center">
                        <div class="card-body">
                            <div style="width: 64px; height: 64px; margin: 0 auto var(--espacio-md); background-color: var(--rojo-100); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="var(--rojo)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <h3>Mejor precio</h3>
                            <p>Compara precios y encuentra las mejores ofertas en tu comuna.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Sports Section -->
        <section class="section" style="background-color: var(--gris-100);">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Deportes populares</h2>
                    <p class="section-subtitle">Encuentra canchas para tu deporte favorito</p>
                </div>
                
                <div class="grid grid-cols-2 grid-cols-md-5 gap-4">
                    <a href="resultados.html?deporte=futbol" class="card card-hover text-center">
                        <div class="card-body">
                            <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2"/>
                                    <path d="M12 2C13.09 8.26 15.74 10.91 22 12C15.74 13.09 13.09 15.74 12 22C10.91 15.74 8.26 13.09 2 12C8.26 10.91 10.91 8.26 12 2Z" stroke="white" stroke-width="2"/>
                                </svg>
                            </div>
                            <h4>Fútbol</h4>
                        </div>
                    </a>
                    
                    <a href="resultados.html?deporte=tenis" class="card card-hover text-center">
                        <div class="card-body">
                            <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2"/>
                                    <path d="M6 9L18 15M6 15L18 9" stroke="white" stroke-width="2"/>
                                </svg>
                            </div>
                            <h4>Tenis</h4>
                        </div>
                    </a>
                    
                    <a href="resultados.html?deporte=padel" class="card card-hover text-center">
                        <div class="card-body">
                            <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="white" stroke-width="2"/>
                                    <path d="M9 9L15 15M15 9L9 15" stroke="white" stroke-width="2"/>
                                </svg>
                            </div>
                            <h4>Pádel</h4>
                        </div>
                    </a>
                    
                    <a href="resultados.html?deporte=basquet" class="card card-hover text-center">
                        <div class="card-body">
                            <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2"/>
                                    <path d="M12 2V22M2 12H22M7 7L17 17M17 7L7 17" stroke="white" stroke-width="2"/>
                                </svg>
                            </div>
                            <h4>Básquet</h4>
                        </div>
                    </a>
                    
                    <a href="resultados.html?deporte=baby_futbol" class="card card-hover text-center">
                        <div class="card-body">
                            <div style="width: 48px; height: 48px; margin: 0 auto var(--espacio-sm); background-color: var(--rojo); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="8" stroke="white" stroke-width="2"/>
                                    <path d="M12 4C12.73 8.39 14.61 10.27 19 11C14.61 11.73 12.73 13.61 12 18C11.27 13.61 9.39 11.73 5 11C9.39 10.27 11.27 8.39 12 4Z" stroke="white" stroke-width="2"/>
                                </svg>
                            </div>
                            <h4>Baby Fútbol</h4>
                        </div>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CanchaCL</h3>
                    <p>La plataforma líder para reservar canchas deportivas en Chile.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Enlaces</h3>
                    <ul>
                        <li><a href="index.html">Inicio</a></li>
                        <li><a href="resultados.html">Buscar Canchas</a></li>
                        <li><a href="#">Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Términos y Condiciones</a></li>
                        <li><a href="#">Política de Privacidad</a></li>
                        <li><a href="#">Política de Cookies</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 CanchaCL - Chile. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../assets/js/app.js" defer></script>
    <script src="../assets/js/api.js" defer></script>
    <script src="../assets/js/ui.js" defer></script>
    <script>
        // Inicializar página de inicio
        document.addEventListener('DOMContentLoaded', function() {
            // Establecer fecha mínima como hoy
            const fechaInput = document.getElementById('fecha');
            const hoy = new Date().toISOString().split('T')[0];
            fechaInput.min = hoy;
            fechaInput.value = hoy;
            
            // Cargar comunas
            if (window.API) {
                window.API.getComunas().then(response => {
                    if (response.success) {
                        const comunaSelect = document.getElementById('comuna');
                        response.data.forEach(comuna => {
                            const option = document.createElement('option');
                            option.value = comuna.nombre;
                            option.textContent = comuna.nombre;
                            comunaSelect.appendChild(option);
                        });
                    }
                }).catch(console.error);
            }
        });
    </script>
</body>
</html>
