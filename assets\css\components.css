/* CanchaCL - Components Styles */

/* Botones */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--espacio-sm);
  padding: var(--espacio-sm) var(--espacio-lg);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radio);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--rojo);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Botón primario */
.btn-primary {
  background-color: var(--rojo);
  color: var(--blanco);
  border-color: var(--rojo);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--rojo-800);
  border-color: var(--rojo-800);
  color: var(--blanco);
  text-decoration: none;
}

/* Botón secundario */
.btn-secondary {
  background-color: var(--blanco);
  color: var(--rojo);
  border-color: var(--rojo);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--rojo-50);
  color: var(--rojo-800);
  text-decoration: none;
}

/* Botón outline */
.btn-outline {
  background-color: transparent;
  color: var(--negro);
  border-color: var(--gris-300);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--gris-100);
  border-color: var(--gris-400);
  color: var(--negro);
  text-decoration: none;
}

/* Botón ghost */
.btn-ghost {
  background-color: transparent;
  color: var(--gris-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--gris-100);
  color: var(--negro);
  text-decoration: none;
}

/* Tamaños de botones */
.btn-sm {
  padding: var(--espacio-xs) var(--espacio-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--espacio-md) var(--espacio-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

.btn-full {
  width: 100%;
}

/* Tarjetas */
.card {
  background-color: var(--blanco);
  border: 1px solid var(--gris-200);
  border-radius: var(--radio);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--espacio-lg);
  border-bottom: 1px solid var(--gris-200);
}

.card-body {
  padding: var(--espacio-lg);
}

.card-footer {
  padding: var(--espacio-lg);
  border-top: 1px solid var(--gris-200);
  background-color: var(--gris-100);
}

.card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-image-lg {
  height: 300px;
}

/* Tarjeta de cancha */
.cancha-card {
  position: relative;
  cursor: pointer;
}

.cancha-card-image {
  position: relative;
  overflow: hidden;
}

.cancha-card-badge {
  position: absolute;
  top: var(--espacio-sm);
  right: var(--espacio-sm);
  z-index: 10;
}

.cancha-card-content {
  padding: var(--espacio-md);
}

.cancha-card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--espacio-xs);
  color: var(--negro);
}

.cancha-card-location {
  font-size: var(--font-size-sm);
  color: var(--gris-600);
  margin-bottom: var(--espacio-sm);
}

.cancha-card-rating {
  display: flex;
  align-items: center;
  gap: var(--espacio-xs);
  margin-bottom: var(--espacio-sm);
}

.cancha-card-price {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--rojo);
}

/* Chips/Badges */
.chip {
  display: inline-flex;
  align-items: center;
  gap: var(--espacio-xs);
  padding: var(--espacio-xs) var(--espacio-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border-radius: var(--border-radius-full);
  white-space: nowrap;
}

.chip-disponible {
  background-color: #DCFCE7;
  color: #166534;
}

.chip-pocas {
  background-color: #FEF3C7;
  color: #92400E;
}

.chip-agotado {
  background-color: #FEE2E2;
  color: #991B1B;
}

.chip-deporte {
  background-color: var(--gris-100);
  color: var(--gris-700);
}

/* Formularios */
.form-group {
  margin-bottom: var(--espacio-lg);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--negro);
  margin-bottom: var(--espacio-xs);
}

.form-input {
  width: 100%;
  padding: var(--espacio-sm) var(--espacio-md);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--negro);
  background-color: var(--blanco);
  border: 1px solid var(--gris-300);
  border-radius: var(--border-radius-sm);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-height: 44px;
}

.form-input:focus {
  outline: none;
  border-color: var(--rojo);
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.form-input:invalid {
  border-color: var(--rojo-500);
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--espacio-sm) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--espacio-xl);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-error {
  font-size: var(--font-size-sm);
  color: var(--rojo-600);
  margin-top: var(--espacio-xs);
}

.form-help {
  font-size: var(--font-size-sm);
  color: var(--gris-600);
  margin-top: var(--espacio-xs);
}

/* Checkbox y Radio */
.form-check {
  display: flex;
  align-items: flex-start;
  gap: var(--espacio-sm);
  margin-bottom: var(--espacio-md);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  accent-color: var(--rojo);
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--negro);
  cursor: pointer;
}

/* Modales */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-backdrop.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background-color: var(--blanco);
  border-radius: var(--radio);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-modal);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.modal.active {
  transform: translate(-50%, -50%) scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--espacio-lg);
  border-bottom: 1px solid var(--gris-200);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--negro);
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--espacio-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background-color: var(--gris-100);
}

.modal-body {
  padding: var(--espacio-lg);
}

.modal-footer {
  display: flex;
  gap: var(--espacio-md);
  justify-content: flex-end;
  padding: var(--espacio-lg);
  border-top: 1px solid var(--gris-200);
}

/* Alertas y Toasts */
.alert {
  padding: var(--espacio-md);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--espacio-md);
}

.alert-success {
  background-color: #DCFCE7;
  color: #166534;
  border: 1px solid #BBF7D0;
}

.alert-error {
  background-color: #FEE2E2;
  color: #991B1B;
  border: 1px solid #FECACA;
}

.alert-warning {
  background-color: #FEF3C7;
  color: #92400E;
  border: 1px solid #FDE68A;
}

.alert-info {
  background-color: #DBEAFE;
  color: #1E40AF;
  border: 1px solid #BFDBFE;
}

/* Loading */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gris-300);
  border-radius: 50%;
  border-top-color: var(--rojo);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .modal {
    max-width: 500px;
  }
  
  .card-image {
    height: 240px;
  }
}

@media (min-width: 768px) {
  .modal {
    max-width: 600px;
  }
  
  .modal-footer {
    justify-content: flex-end;
  }
}
